import React, { useState, useEffect, useCallback, useRef } from "react";
import { createPortal } from "react-dom";
import wx from "weixin-webview-jssdk";
import { View, Image, Text } from "@tarojs/components";
import {
  Button,
  Cell,
  PullRefresh,
  LoadMore,
  Loading,
  SearchBar,
  Dialog,
  DatePicker,
  Tabs,
  Sticky,
} from "@arco-design/mobile-react";
import { IconLoadEmpty } from "@/components/YkIcons";
import { IconImage, IconClose } from "@arco-iconbox/react-yk-arco";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import {
  getUserInfo,
  getOrderList,
  cancelOrder,
  getOrderPayParams,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar";
import Taro, { useReachBottom, usePageScroll, useDidShow } from "@tarojs/taro";
import "./index.less";
import { toast } from "@/utils/yk-common";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";
import FilterDrawer, { FilterDrawerRef } from "@/components/FilterDrawer";
import { AuthTypes } from "@/utils/config/authTypes";
import { usePermission } from "@/hooks/usePermission";
import { IconPayment } from "@/components/YkIcons";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
const tabList = ["全部", "待付款", "已付款", "已发货", "已完成", "退款","已取消"];

// 转换为Arco Tabs组件所需的格式
const tabData = tabList.map((title, index) => ({ title, index }));

export default function OrderPage() {
  const [activeTab, setActiveTab] = useState(0); // 默认高亮"待付款"
  const [orderList, setOrderList] = useState<any[]>([]);
  const [searchKeyword, setSearchKeyword] = useState(""); // 搜索关键词
  const searchKeywordRef = useRef(""); // 实时保存搜索内容
  const searchImgRef = useRef(""); // 保存图片搜索内容
  var userInfo = Taro.getStorageSync("userInfo");

  // 弹框相关状态
  const [isPopupVisible, setPopupVisible] = useState(false);
  const operationTypeRef = useRef(""); // 操作类型标识：'imageSearch'
  const chooseImageRef = useRef(""); // 选择图片类型标识：'album' 或 'camera'

  // 筛选抽屉相关状态
  const [isFilterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const filterDrawerRef = useRef<FilterDrawerRef>(null);

  // 日期选择器相关状态
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [currentDateType, setCurrentDateType] = useState<1 | 2>(1); // 1: 起始时间, 2: 结束时间

  // 临时时间选择状态（在抽屉中显示，但未确认）
  const [tempBeginDate, setTempBeginDate] = useState('请选择');
  const [tempEndDate, setTempEndDate] = useState('请选择');

  // 已确认的筛选时间（用于显示筛选条件和接口调用）
  const [confirmedBeginDate, setConfirmedBeginDate] = useState('请选择');
  const [confirmedEndDate, setConfirmedEndDate] = useState('请选择');

  // 保存当前的筛选条件
  const [currentFilterTime, setCurrentFilterTime] = useState<{createTime?: string, endTime?: string}>({});

    // 选择图片的方法
    const chooseImage = (sourceType: "album" | "camera") => {
      if(platformRef.current === "Android"){
      window.setPhotoNum?.setPhotoNum(1);
      }
      if(platformRef.current === "HM"){
        window.harmony.setPhotoNum(1);
      }
      console.log(sourceType);
      Taro.chooseImage({
        count: 1,
        sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
        success: async (res) => {
          const imagePath = res.tempFilePaths[0];
          //转为base64
          const base64Data = await blobToBase64(imagePath);
          setSearchKeyword("");
          searchKeywordRef.current = ""; // 同时清除文字搜索的ref
          searchImgRef.current = base64Data;
          pageRef.current = 1;
          setIsLoadEnd(false);
          // 图片搜索时，同时传递当前的筛选条件
          getOrderListData(currentOrderTypeRef.current, true, currentFilterTime.createTime, currentFilterTime.endTime);
        },
        fail: (err) => {},
      });
    };
    

    const handleUrlParams = async () => {
      try {
        // 获取URL参数
        const params = Taro.getCurrentInstance().router?.params || {};
        console.log("params", params);
        const { userId, accessToken } = params;
        
        if (userId && accessToken) {
          console.log('检测到URL参数:', { userId, accessToken });
          
          // 创建临时用户信息对象，包含token信息
          const tempUserInfo = {
            id: userId,
            userId: userId,
            accessToken: accessToken,
            token: accessToken, // 兼容不同的token字段名
          };
          
          // 先保存临时用户信息，确保请求时有token
          Taro.setStorageSync('userInfo', tempUserInfo);
          
          // 请求完整的用户信息
          const userInfoRes = await getUserInfo();
          if (userInfoRes && userInfoRes.code === 0) {
            // 合并token信息到完整用户信息中
            const completeUserInfo = {
              ...userInfoRes.data,
              accessToken: accessToken,
              token: accessToken,
            };
            
            // 保存完整用户信息到本地
            Taro.setStorageSync('userInfo', completeUserInfo);
            console.log('用户信息获取成功:', completeUserInfo);
            userInfo = completeUserInfo
            
            // 刷新页面数据
            handleRefresh();
          } else {
            console.error('获取用户信息失败:', userInfoRes.msg);
            toast("error", {
              content: userInfoRes.msg || "获取用户信息失败",
              duration: 2000,
            });
          }
        }
      } catch (error) {
        console.error('处理URL参数失败:', error);
        toast("error", {
          content: "登录失败，请重试",
          duration: 2000,
        });
      }
    };

  // 自定义权限同意处理，处理 order 页面特有的逻辑
  const customWebPermissonConsent = () => {
    console.log("order customWebPermissonConsent");
    console.log("operationType:", operationTypeRef.current);
    console.log("chooseImageType:", chooseImageRef.current);

    // 根据操作类型执行不同的后续处理
    if (operationTypeRef.current === "imageSearch") {
      // 以图搜图操作
      console.log("执行图片选择，类型:", chooseImageRef.current);
      chooseImage(chooseImageRef.current as "album" | "camera");
    }

    // 清除操作类型标识
    operationTypeRef.current = "";

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);

  // 分页和加载状态
  const [loading, setLoading] = useState(false);
  const [canPullRefresh, setCanPullRefresh] = useState(true);
  const [isLoadEnd, setIsLoadEnd] = useState(false);
  const [loadStatus, setLoadStatus] = useState<LoadMoreStatus>("prepare");
  const pageRef = useRef(1);
  const pageSizeRef = useRef(10);
  const isFetchingRef = useRef(false);
  const currentOrderTypeRef = useRef<number | undefined>(0);

  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();
    
    // 使用立即执行的异步函数等待URL参数处理完成
    (async () => {
      await handleUrlParams();
      
      // URL参数处理完成后，再检查登录状态
      const currentUserInfo = Taro.getStorageSync("userInfo");
      console.log('userInfo', currentUserInfo);
      
      if(!currentUserInfo || currentUserInfo.accessToken === undefined){
        // 弹窗提示用户去登录
        Dialog.confirm({
          title: "登录提示",
          children: "您还未登录，请先登录后再操作。",
          okText: "去登录",
          cancelText: "取消",
          platform: "ios",
          onOk: () => {
            // 跳转到登录页，这里假设有 login 页面
            if(platformRef.current === "WX"){
              wx.miniProgram.reLaunch({ url: "/pages/index/index?fromOrder=true" });
            }else{
              Taro.reLaunch({ url: "/pages/login/index" });
            }
          },
        });
      }
    })();
    
    //刷新订单事件
    Taro.eventCenter.on('refreshOrderList', handleRefresh);

    // 获取URL参数中的current值
    const instance = Taro.getCurrentInstance();
    const currentParam = instance.router?.params?.current;

    console.log('currentParam',currentParam);
    // 如果有current参数，设置对应的tab，否则使用默认值
    const initialTab = currentParam ? parseInt(currentParam, 10) : activeTab;
    console.log('initialTab',initialTab);
    console.log('activeTab',activeTab);
    if (initialTab !== activeTab) {
      setActiveTab(initialTab);
    }

    handleTabChange(initialTab);

    // 使用全局回调管理器注册支付回调
    const callbackCleanup = useGlobalCallbacks('orderPage', {
      webPaySuc: webPaySuc,
      wxPayWithStr: webPaySuc,
      aliPayWithStr: webPaySuc,
    });

    // 清理事件监听
    return () => {
      cleanup && cleanup();
      Taro.eventCenter.off('refreshOrderList', handleRefresh);
      callbackCleanup && callbackCleanup();
    };
  }, []);

  // 监听页面显示，用于微信小程序支付后返回刷新
  const isFirstShowRef = useRef(true);
  useDidShow(() => {
    console.log("页面显示，检查是否需要刷新订单");
    // 第一次显示是页面初始化，不需要刷新（因为useEffect中已经加载了数据）
    if (isFirstShowRef.current) {
      isFirstShowRef.current = false;
      return;
    }
    
    // 微信小程序环境下，从其他页面返回时刷新订单列表
    // 这样可以确保从支付页面返回后能及时更新订单状态
    if (platformRef.current === "WX") {
      console.log("微信小程序环境：页面显示时刷新订单列表");
      handleRefresh();
    }
  });

  const webPaySuc = (status: number) => {
    console.log("webPaySuc", status);
    if (status === 1) {
      toast("success", {
        content: "支付成功",
        duration: 2000,
      });
      //刷新订单列表
      handleRefresh();
    } else if (status === 2) {
      toast("error", {
        content: "支付失败",
        duration: 2000,
      });
    } else if (status === 3) {
      toast("error", {
        content: "支付取消",
        duration: 2000,
      });
    }
  };

  // 监听页面滚动，控制下拉刷新
  usePageScroll(({ scrollTop }) => {
    // 当滚动位置为0时才允许下拉刷新
    setCanPullRefresh(scrollTop === 0);
  });

  // 格式化时间戳为可读格式
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    return `${month}-${day} ${hours}:${minutes}`;
  };

  // 根据订单状态获取状态文本和tab值
  const getOrderStatus = (order: any) => {
    // 根据orderType字段映射状态
    // orderType: 1代付款 2已付款 3已发货 4已完成 5退款 6已取消
    switch (order.status) {
    // switch (currentOrderTypeRef.current) {
      case 1:
        return { status: "待付款", statusText: "待付款", tab: 1 };
      case 2:
        return { status: "已付款", statusText: "已付款", tab: 2 };
      case 3:
        if(order.shipmentStatus === 1){
          return { status: "全部已发货", statusText: "全部已发货", tab: 3 };
        }else{
          return { status: "部分未发货", statusText: "部分未发货", tab: 3 };
        }
      case 4:
        return { status: "已完成", statusText: "已完成", tab: 4 };
      case 5:
        return { status: "退款中", statusText: "退款中", tab: 5 };
      case 6:
        return { status: "已退款", statusText: "已退款", tab: 6 };
      case 7:
        return { status: "已取消", statusText: "已取消", tab: 7 }; 
      default:
        return { status: "待付款", statusText: "待付款", tab: 1 };
    }
  };

  const getOrderListData = useCallback(
    async (orderType?: number, isRefresh = false, createTime?: string, endTime?: string) => {
      // 防重入检查
      if (isFetchingRef.current) {
        console.log("Already fetching, skip request");
        return;
      }

      console.log(
        "getOrderListData called, page:",
        pageRef.current,
        "isLoadEnd:",
        isLoadEnd,
        "loading:",
        loading
      );

      try {
        // 首页加载显示loading，分页加载显示LoadMore的loading
        if (pageRef.current === 1 && !loading) {
          setLoading(true);
        } else if (pageRef.current > 1) {
          setLoadStatus("loading");
        }

        isFetchingRef.current = true;

        const data: any = {
          userId: userInfo.id,
          pageNo: pageRef.current,
          pageSize: pageSizeRef.current,
          // orderTypeFlag: 0,
        };

        // 如果传入了orderType参数，则添加到请求数据中
        if (orderType && orderType > 0) {
          if(orderType==6){
            data.status = 7;
          }else{
            data.status = orderType;
          }
        }

        // 判断搜索框有值就传content
        const keyword = searchImgRef.current?.length > 0 ? searchImgRef.current : searchKeywordRef.current;
        if (keyword && keyword.length > 0) {
          data.content = keyword;
        }

        // 添加时间筛选参数
        if (createTime) {
          data.createTime = createTime;
        }
        if (endTime) {
          data.endTime = endTime;
        }

        const orderListData: any = await getOrderList(data);

        if (orderListData && orderListData.code == 0) {
          const newData = orderListData.data.list || [];

          // 转换数据格式以适配现有的UI组件
          const formattedOrderList = newData.map((item: any) => {
            const statusInfo = getOrderStatus(item);

            // 处理商品列表
            const goods = item.orderDynamics.map((dynamic: any) => {
              // 获取第一张图片作为商品图片
              const pictures = dynamic.dynamicPictures
                ? dynamic.dynamicPictures.split(",")
                : [];
              const firstPicture = pictures.length > 0 ? pictures[0] : "";

              // 组合规格信息
              const specs = dynamic.orderDetails
                .map((detail: any) => `${detail.colorName} ${detail.specName}`)
                .join(", ");

              return {
                img: firstPicture,
                name: `${dynamic.dynamicName}`,
                price: dynamic.price,
                count: dynamic.count,
                orderDetails: dynamic.orderDetails,
              };
            });

            return {
              id: item.id,
              shop: item.userName || "",
              status: statusInfo.status,
              statusText: statusInfo.statusText,
              tab: statusInfo.tab,
              goods: goods,
              express: item.delivery, // 默认值，可以根据实际情况调整
              time: formatTime(item.createTime),
              total: item.totalPrice/100,
              orderType:item.status,
              freight: item.deliveryPrice/100||0, // 接口中没有运费字段，设为0
              remark: item.remark,
              pictureRemark: item.pictureRemark,
              combineOutTradeNo: item.combineOutTradeNo,
              orderRefund: item.refund,
              originalData: item, // 保留原始数据以备后用
            };
          });

          // 根据是否是刷新来决定如何更新数据
          if (isRefresh || pageRef.current === 1) {
            setOrderList(formattedOrderList);
          } else {
            setOrderList((prev) => [...prev, ...formattedOrderList]);
          }

          // 判断是否还有更多数据
          if (newData.length < pageSizeRef.current) {
            setIsLoadEnd(true);
            setLoadStatus("nomore");
            console.log("No more data available");
          } else {
            pageRef.current += 1;
            setLoadStatus("prepare");
            setIsLoadEnd(false);
            console.log("More data available, next page:", pageRef.current);
          }

          console.log("格式化后的订单数据:", formattedOrderList);
        } else {
          setLoadStatus("retry");
          console.error("API error:", orderListData.msg);
        }
      } catch (error) {
        console.error("Request failed:", error);
        setLoadStatus("retry");
      } finally {
        setLoading(false);
        isFetchingRef.current = false;
      }
    },
    [userInfo.id, isLoadEnd, loading]
  );

  // 处理tab切换
  const handleTabChange = useCallback(
    (tabIndex: number) => {
      setActiveTab(tabIndex);

      // 重置分页状态
      pageRef.current = 1;
      setIsLoadEnd(false);
      setLoadStatus("prepare");

      // 保存当前的orderType
      const orderType = tabIndex === 0 ? undefined : tabIndex;
      currentOrderTypeRef.current = orderType;

      // 根据tab索引获取对应的orderType，同时传递当前的筛选条件
      // tabList = ["全部", "待付款", "已付款", "已发货", "已完成", "退款"]
      // 对应的orderType: 0=全部, 1=待付款, 2=已付款, 3=已发货, 4=已完成, 5=退款
      getOrderListData(orderType, true, currentFilterTime.createTime, currentFilterTime.endTime);
    },
    [getOrderListData, currentFilterTime]
  );

  // 下拉刷新处理函数
  const handleRefresh = useCallback(async () => {
    console.log("下拉刷新开始");

    // 重置分页和状态
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");

    // 调用数据获取方法，传递当前的筛选条件
    await getOrderListData(currentOrderTypeRef.current, true, currentFilterTime.createTime, currentFilterTime.endTime);

    console.log("下拉刷新完成");
  }, [getOrderListData, currentFilterTime]);

  // 上拉加载更多
  useReachBottom(() => {
    console.log(
      "useReachBottom triggered, isLoadEnd:",
      isLoadEnd,
      "isFetching:",
      isFetchingRef.current,
      "loading:",
      loading
    );

    // 简化检查逻辑
    if (!loading && !isLoadEnd && !isFetchingRef.current) {
      console.log("Triggering load more from useReachBottom");
      getOrderListData(currentOrderTypeRef.current, false, currentFilterTime.createTime, currentFilterTime.endTime);
    }
  });

  // 处理取消订单
  const handleCancelOrder = useCallback(
    async (orderId: number | string) => {
      try {
        Dialog.confirm({
          title: "确认取消？",
          children: "取消后订单将会失效且无法恢复,请谨慎操作!",
          okText: "确定",
          cancelText: "取消",
          platform: "ios",
          onOk: async () => {
            // 显示加载提示
            Taro.showLoading({
              title: "正在取消订单...",
            });

            // 调用取消订单接口
            const cancelResult = await cancelOrder({ id: orderId });

            Taro.hideLoading();

            if (cancelResult && cancelResult.code === 0) {
              // 取消成功，显示成功提示
              toast("success", {
                content: "订单已取消",
                duration: 2000,
              });

              // 刷新订单列表
              handleRefresh();
            } else {
              // 取消失败，显示错误信息
              toast("error", {
                content: cancelResult.msg || "取消订单失败，请重试",
                duration: 2000,
              });
            }
          },
        });
      } catch (error) {
        Taro.hideLoading();
        console.error("取消订单失败:", error);
        toast("error", {
          content: "取消订单失败，请检查网络连接",
          duration: 2000,
        });
      }
    },
    [handleRefresh]
  );

  // 处理支付逻辑（参考订单详情实现）
  const handlePayment = useCallback(async (orderId: number | string) => {
    try {
      if (!orderId) {
        toast("error", {
          content: "订单ID不存在",
          duration: 2000,
        });
        return;
      }

      // 获取支付参数
      const payParamsResponse = await getOrderPayParams({ orderId: orderId, channelCode: platformRef.current === "WX" ? "wx_lite" : "wx_app" });

      if (payParamsResponse && payParamsResponse.code === 0) {
        var payParams = payParamsResponse.data;
        console.log("支付参数:", payParams);

        if(platformRef.current === "HM"){
          window.harmony.wxPay(JSON.stringify(payParams));
        }else if(platformRef.current === "IOS"){
          window.webkit.messageHandlers.wxPayWithStr.postMessage(JSON.stringify(payParams));
        }else if(platformRef.current === "Android"){
          window.wxPay.wxPay(JSON.stringify(payParams));
        } else if (platformRef.current === "WX") {
          payParams.userOrderId = orderId
          wx.miniProgram.navigateTo({
            url: '/pages/wxpay/index?payParams=' + encodeURIComponent(JSON.stringify(payParams))
          });
        }
      } else {
        toast("error", {
          content: payParamsResponse.msg || "获取支付信息失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("支付处理失败:", error);
      toast("error", {
        content: "支付处理失败",
        duration: 2000,
      });
    }
  }, []);

  // 处理搜索
  const handleSearch = useCallback(
    (keyword: string) => {
      setSearchKeyword(keyword);
      searchKeywordRef.current = keyword;
      searchImgRef.current = "";
      // 重置分页状态
      pageRef.current = 1;
      setIsLoadEnd(false);
      setLoadStatus("prepare");

      // 调用搜索接口，同时传递当前的筛选条件
      getOrderListData(currentOrderTypeRef.current, true, currentFilterTime.createTime, currentFilterTime.endTime);
    },
    [getOrderListData, currentFilterTime]
  );

  // 打开选择图片方式弹窗
  const openPopup = () => {
    if (platformRef.current === "WX") {
      chooseImageRef.current = "album";
      chooseImage("album");
      return;
    }
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    operationTypeRef.current = "imageSearch";
    if (index === 0) {
      // 设置图片选择类型为拍照
      chooseImageRef.current = "camera";
      if(platformRef.current === "HM"){
        chooseImage("camera");
      }else{
      // 请求相机权限
      if (!hasPermission(AuthTypes.CAMERA)) {
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.CAMERA);
        return;
      }
      chooseImage("camera");
      }
    } else if (index === 1) {
      // 设置图片选择类型为相册
      chooseImageRef.current = "album";
      if(platformRef.current === "HM"){
        chooseImage("album");
      }else{
      // 请求相册权限
      if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
        console.log("相册权限请求");
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.GALLERY_PHOTO);
        return;
      }
      console.log("相册权限");
      chooseImage("album");
      }
    }
  };

  const blobToBase64 = async (blobUrl: string): Promise<string> => {
    try {
      // 首先通过fetch获取blob数据
      const response = await fetch(blobUrl);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          // reader.result包含base64字符串
          const base64String = reader.result as string;
          // 移除开头的 "data:image/jpeg;base64," 部分（如果后端不需要这个前缀）
          const base64Data = base64String.split(",")[1];
          resolve(base64Data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("转换base64失败:", error);
      throw error;
    }
  };



  // 搜索框onChange处理
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchKeyword(value); // 立即更新输入框的值
    searchKeywordRef.current = value; // 同时更新ref

    // 如果输入为空，立即清除搜索结果，同时传递当前的筛选条件
    if (value.length === 0) {
      searchImgRef.current = "";
      pageRef.current = 1;
      setIsLoadEnd(false);
      getOrderListData(currentOrderTypeRef.current, true, currentFilterTime.createTime, currentFilterTime.endTime);
    }
  };

  // 处理键盘按键事件
  const handleKeyDown = (e) => {
    console.log("Key pressed:", e.key);
    if (e.key === 'Enter') {
      console.log("Enter key pressed, searching...");
      handleSearchSubmit();
    }
  };

  // 处理键盘搜索按钮点击
  const handleSearchSubmit = (value) => {
    console.log("handleSearchSubmit called with:", value);
    const searchValue = value || searchKeyword;
    if (searchValue.trim().length > 0) {
      handleSearch(searchValue.trim());
    }
  };

  // 搜索框清除处理
  const handleSearchClear = () => {
    setSearchKeyword("");
    searchKeywordRef.current = ""; // 同时清除ref
    searchImgRef.current = "";
    pageRef.current = 1;
    setIsLoadEnd(false);
    // 清除搜索时，保持当前的筛选条件
    getOrderListData(currentOrderTypeRef.current, true, currentFilterTime.createTime, currentFilterTime.endTime);
  };

  // 处理筛选按钮点击
  const handleFilter = useCallback(() => {
    console.log("点击筛选按钮");
    setFilterDrawerVisible(true);
  }, []);

  // 处理筛选抽屉关闭
  const handleFilterDrawerClose = useCallback(() => {
    setFilterDrawerVisible(false);
  }, []);

  // 处理筛选确认
  const handleFilterConfirm = useCallback((data: any) => {
    console.log("筛选确认", data);

    // 验证时间选择的有效性
    if (tempBeginDate !== '请选择' && tempEndDate !== '请选择') {
      const beginTime = new Date(tempBeginDate).getTime();
      const endTime = new Date(tempEndDate).getTime();

      if (endTime < beginTime) {
        toast("error", {
          content: "结束时间必须大于起始时间",
          duration: 2000,
        });
        return;
      }
    }

    // 将日期字符串转换为指定格式
    let createTime: string | undefined;
    let endTime: string | undefined;

    if (tempBeginDate && tempBeginDate !== '请选择') {
      createTime = tempBeginDate + ' 00:00:00';
    }

    if (tempEndDate && tempEndDate !== '请选择') {
      endTime = tempEndDate + ' 23:59:59';
    }

    // 确认临时选择的时间
    setConfirmedBeginDate(tempBeginDate);
    setConfirmedEndDate(tempEndDate);

    // 保存当前筛选条件
    const filterTime = { createTime, endTime };
    setCurrentFilterTime(filterTime);

    // 重置分页状态
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");

    // 调用获取订单列表接口，传递时间参数
    const orderType = currentOrderTypeRef.current;
    getOrderListData(orderType, true, createTime, endTime);

    // 验证成功，关闭抽屉
    setFilterDrawerVisible(false);
  }, [tempBeginDate, tempEndDate, getOrderListData]);

  // 处理筛选重置
  const handleFilterReset = useCallback(() => {
    console.log("筛选重置");
    filterDrawerRef.current?.reset();
    setTempBeginDate('请选择');
    setTempEndDate('请选择');
    setConfirmedBeginDate('请选择');
    setConfirmedEndDate('请选择');

    // 清空筛选条件
    setCurrentFilterTime({});

    // 重置分页状态
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");

    // 重新获取数据，不传递时间参数
    const orderType = currentOrderTypeRef.current;
    getOrderListData(orderType, true);
  }, [getOrderListData]);

  // 处理时间选择点击
  const handleTimeClick = useCallback((type: 1 | 2) => {
    setCurrentDateType(type);
    setDatePickerVisible(true);
  }, []);

  // 日期选择确认
  const handleDateConfirm = useCallback((value: string) => {
    if (currentDateType === 1) {
      setTempBeginDate(value);
      filterDrawerRef.current?.setDateRange(value, tempEndDate);
    } else {
      setTempEndDate(value);
      filterDrawerRef.current?.setDateRange(tempBeginDate, value);
    }
    setDatePickerVisible(false);
  }, [currentDateType, tempBeginDate, tempEndDate]);

  // 日期选择关闭
  const handleDatePickerClose = useCallback(() => {
    setDatePickerVisible(false);
  }, []);

  // 获取筛选条件显示文本
  const getFilterText = useCallback(() => {
    const parts = [];
    if (confirmedBeginDate && confirmedBeginDate !== '请选择') {
      parts.push(`${confirmedBeginDate}`);
    }
    if (confirmedEndDate && confirmedEndDate !== '请选择') {
      parts.push(`${confirmedEndDate}`);
    }
    return parts.join('，');
  }, [confirmedBeginDate, confirmedEndDate]);

  // 处理清除筛选条件
  const handleClearFilter = useCallback(() => {
    handleFilterReset();
  }, [handleFilterReset]);

  // 处理查看图片
  const handleViewImages = useCallback((images: string[]) => {
    console.log("handleViewImages", images);
    if (images && images.length > 0) {
      // Taro.previewImage({
      //   urls: images,
      //   current: images[0],
      // });

        // 跳转到独立的图片预览页面
     const params = {
      images: encodeURIComponent(JSON.stringify(images)),
      initialIndex: 0,
      dynamic: "",
      showCar: "false",
      showBottom: "false",
    };
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join("&");
    Taro.navigateTo({
      url: `/pageDynamic/imagePreview/index?${queryString}`,
    });
    } else {
      toast("info", {
        content: "暂无图片",
        duration: 1500,
      });
    }

   
  }, []);


  // 处理再来一单逻辑
  const handleReorder = useCallback((order: any) => {
    console.log("再来一单订单数据:", order);

    if (!order.originalData || !order.originalData.orderDynamics) {
      toast("error", {
        content: "订单商品信息不存在",
        duration: 2000
      });
      return;
    }

    // 从订单数据中提取商品信息
    const userOrderDynamic = order.originalData.orderDynamics[0];

    // 构建details数组，从订单中提取SKU信息
    const details = userOrderDynamic.orderDetails?.map((orderDetail: any) => ({
      id: Date.now() + Math.random(),
      userId: order.originalData.userId,
      shoppingCartId: Date.now() + Math.random(),
      productColorId: orderDetail.productColorId, // 订单中没有colorId，需要根据colorName查找
      productSpecificationsId: orderDetail.productSpecificationsId || null, // 订单中没有specId，需要根据specName查找
      productSpecificationsName: orderDetail.specName || null,
      productColorName: orderDetail.colorName || null,
      quantity: orderDetail.quantity || 1,
      price: userOrderDynamic.price || 0,
      checked: true
    })) || [{
      id: Date.now() + Math.random(),
      userId: order.originalData.userId,
      shoppingCartId: Date.now() + Math.random(),
      productColorId: null,
      productSpecificationsId: null,
      productSpecificationsName: null,
      productColorName: null,
      quantity: 1,
      price: userOrderDynamic.price || 0,
      checked: true
    }];

    // 构建selectedItems数据结构
    const selectedItems = [{
      shoppingCartList: [{
        id: Date.now(),
        shoppingCartSellerId: order.originalData.sellerUserId,
        dynamicsId: userOrderDynamic.dynamicsId,
        dynamicsContent: userOrderDynamic.dynamicName || '',
        dynamicsImage: userOrderDynamic.dynamicPictures || '',
        remark: '', // 再来一单时备注为空
        type: 1,
        price: userOrderDynamic.price || 0,
        checked: true,
        details: details,
        isCollect: 0
      }],
      shoppingCartSellerId: order.originalData.sellerUserId,
      shoppingCartSellerUserId: order.originalData.sellerUserId,
      dynamicsUserName: order.originalData.dynamicUserName || '',
      dynamicsUserAvatar: order.originalData.dynamicUserAvatar || '',
      checked: true
    }];

    console.log("构建的selectedItems:", selectedItems);

    // 存储到本地存储
    Taro.setStorageSync('selectedItems', selectedItems);

    // 跳转到确认订单页面
    Taro.navigateTo({
      url: '/pageOrder/order/pay/index?from=detail'
    });
  }, []);

  // 处理按钮点击事件
  const handleButtonClick = useCallback(
    (buttonText: string, order: any) => {
      console.log(`点击了${buttonText}按钮`, order);

      switch (buttonText) {
        case "取消订单":
          handleCancelOrder(order.id);
          break;
        case "立即支付":
          // 处理支付逻辑
          handlePayment(order.id);
          break;
        case "再来一单":
          // 处理再来一单逻辑
          handleReorder(order);
          break;
        default:
          console.log("未知按钮:", buttonText);
      }
    },
    [handleCancelOrder, handlePayment, handleReorder]
  );

  return (
    <View className="order-page">
      {platformRef.current !== "WX" &&<YkNavBar title="我买的" />}
      <Tabs
        style={platformRef.current !== "WX" ? { top: '80px' } : { top: '0' }}
        className="order-tabs"
        tabs={tabData}
        activeTab={activeTab}
        tabBarHasDivider={false}
        tabBarScroll
        useCaterpillar
        onChange={(tab, index) => {
          console.log('[tabs onChange]', tab, index);
          handleTabChange(index);
        }}
      >
        {tabData.map((_, index) => (
          <View key={index}>
            {/* 空内容，实际内容在下方渲染 */}
          </View>
        ))}
      </Tabs>

      {/* 搜索栏 */}
      <View className="searchLine" style={platformRef.current !== "WX" ? { top: '122px' } : { top: '42px' }}>
        <SearchBar
          placeholder="商品名称"
          clearable={true}
          value={searchKeyword}
          onChange={handleSearchChange}
          onClear={handleSearchClear}
          onKeyDown={handleKeyDown}
          // suffix={
          //   <Image
          //     className="search-icon"
          //     src={require("@/assets/images/common/search_img_icon.png")}
          //     mode="aspectFit"
          //     onClick={() => openPopup()}
          //   />
          // }
          // actionButton={
          //   <View className="demo-search-btn" onClick={handleFilter}>
          //     筛选
          //   </View>
          // }
        />
        
      {/* 筛选条件显示 */}
      {(confirmedBeginDate !== '请选择' || confirmedEndDate !== '请选择') && (
        <View className="filter">
          <View className="filter-text">
            <Text>{getFilterText()}</Text>
          </View>
          {/* <Image
            className="filter-img clickOpacity"
            src={require("@/assets/images/common/clear_filter_icon.png")}
            mode="aspectFit"
            onClick={handleClearFilter}
          /> */}
            <IconClose className="filter-img" onClick={handleClearFilter} />
        </View>
      )}
      </View>


      <PullRefresh
        disabled={loading || !canPullRefresh}
        onRefresh={handleRefresh}
        finishDelay={1000}
        loadingText={
          <View className="pull-refresh-loading">
            <Loading type="dot" radius={8} />
            <Text className="loading-text">正在刷新...</Text>
          </View>
        }
        finishText={<Text className="pull-refresh-success">刷新成功</Text>}
      >
        <View className="order-list" style={{ marginTop: confirmedEndDate !== '请选择'||confirmedBeginDate !== '请选择' ? '135px' : '100px' }}>
          {orderList.map((order, idx) => (
            <View className="order-card" key={idx}>
              <Cell
                bordered={false}
                label={
                  <View className="shop-info" onClick={(e) =>{
                    e.stopPropagation();
                    if(order.originalData?.sellerUserId === userInfo.userId){
                      Taro.navigateTo({
                        url: `/pageDynamic/album/index`,
                      })
                    }else{
                      Taro.navigateTo({
                        url: `/pageUserInfo/userDetail/index?userId=${order.originalData?.sellerUserId}`,
                      })
                    }
                  }}>
                    <Image
                      className="shop-avatar"
                      src={
                        order.originalData?.userAvatar ||
                        ""
                      }
                      mode="aspectFill"
                    />
                    <Text className="shop-name">{order.shop}</Text>
                    {/* <Image
                      className="verified-icon"
                      src={require("@/assets/images/common/wx_pay.png")}
                      mode="aspectFit"
                    /> */}
                    <IconPayment className="verified-icon" />
                  </View>
                }
                text={
                  (<Text className="order-status">{order.status}</Text>) as any
                }
                onClick={() =>
                  Taro.navigateTo({
                    url: `/pageOrder/order/details/index?id=${order.id}`,
                  })
                }
                showArrow
              />

              <View className="goods-list"
              onClick={() =>
                Taro.navigateTo({
                  url: `/pageOrder/order/details/index?id=${order.id}`,
                })
              }>
                {order.goods.map((g, i) => (
                  <View className="goods-item" key={i}>
                    <Image
                      className="goods-img"
                      src={g.img}
                      mode="aspectFill"
                    />
                    <View className="goods-info">
                      <View className="goods-title-row">
                        <Text className="goods-title">{g.name}</Text>
                        <View className="goods-price-section">
                          <Text className="goods-price">￥{(g.price/100).toFixed(2)}</Text>
                          <Text className="goods-quantity">x{g.count}</Text>
                        </View>
                      </View>
                      {/* 显示SKU信息 */}
                      {g.orderDetails &&
                        g.orderDetails.map((detail: any, detailIdx: number) => (
                          <View className="goods-sku" key={detailIdx}>
                            <Text className="sku-text">
  {(() => {
    try {
      const props = JSON.parse(detail.properties || "{}");
      return `${props.colors || ""} ${props.specifications || ""}`.trim();
    } catch (e) {
      return detail.properties; // 如果解析失败，就原样显示
    }
  })()}
</Text>

                              <View className="sku-count">
                              {detail.refundQuantity > 0 && (
                                <Text className="refund-quantity">{`退款x${detail.refundQuantity}`}</Text>
                              )}
                              <Text className="normal-quantity">
                              {`x${detail.quantity} ${order.orderType === 3 ? `(未发货${detail.quantity - detail.quantityShipped})` : ''}`}
                              </Text>
                            </View>
                          </View>
                        ))}
                    </View>
                  </View>
                ))}
              </View>
              {/* 备注信息 */}
              {(order.remark || order.pictureRemark) && (
                <View className="remark-section">
                  <View className="remark-row">
                    <Text className="remark-label">买家留言：</Text>
                    <View className="remark-label-block">
                      {order.pictureRemark &&
                      <View
                        className="remark-img-btn"
                        onClick={() =>

                          handleViewImages(order.pictureRemark.split(",") || [])

                        }
                      >
                        {/* <Image
                          className="order-img-icon"
                          src={require("@/assets/images/common/order_img.png")}
                        /> */}
                        <IconImage className="order-img-icon" />
                        <Text className="img-btn-text">查看图片</Text>
                      </View>
                      }
                      <View>
                        {order.remark ? (
                          <Text className="remark-content">
                            {order.remark || ""}
                          </Text>
                        ) : null}
                      </View>
                    </View>
                  </View>
                </View>
              )}

              <View className="order-summary"
              onClick={() =>
                Taro.navigateTo({
                  url: `/pageOrder/order/details/index?id=${order.id}`,
                })
              }>
                <Text className="express">{order.express}</Text>
                <View className="order-summary-right">
                <Text className="order-time"></Text>
                <Text className="order-total">
                  <View className="time">{order.time}</View>
                  <Text className="goods-count">共{order.goods[0].count}件</Text>
                  <Text className="total-price">￥{(order.total+order.freight).toFixed(2)}</Text>
                  {/* <Text className="total-price">￥{(order.goods[0].count*order.goods[0].price/100+order.freight).toFixed(2)}</Text> */}
                  <Text className="freight-text">
                    (含运费￥{order.freight})
                  </Text>
                </Text>
                {order.orderRefund && order.orderRefund.refundAmount > 0 && (
                          <View className="refund-amount">
                          <Text className="refund-label">已退</Text>
                          <Text className="refund-value">￥{(order.orderRefund.refundAmount+order.orderRefund.refundPostage).toFixed(2)}</Text>
                        </View>
                )}
</View>
              </View>

{order.orderType !== 2 && (
              <View className="order-actions">
                {(() => {
                  const getButtonsByStatus = (status: string) => {
                    switch (status) {
                      case "待付款":
                        return ["取消订单",  "立即支付"];
                      case "已付款":
                        return [];
                      case "部分未发货":
                        return [ "再来一单"];
                        case "全部已发货":
                          return [ "再来一单"];
                      case "已完成":
                        return [ "再来一单"];
                      case "退款":
                      case "退款中":
                        return ["再来一单"];
                      default:
                        return ["再来一单"];
                    }
                  };

                  const buttons = getButtonsByStatus(order.status);
                  return buttons.map((btn) => (
                    <Button
                      className={`order-btn ${
                        btn === "立即支付"
                          ? "pay-btn"
                          : btn === "取消订单"
                          ? "cancel-btn"
                          : "normal-btn"
                      }`}
                      size="small"
                      key={btn}
                      onClick={() => {
                        handleButtonClick(btn, order);
                      }}
                    >
                      {btn}
                    </Button>
                  ));
                })()}
              </View>
)}
            </View>
          ))}
               {orderList.length === 0 && (
            <View className="not_content_order">
            <IconLoadEmpty className="not_content_order-image" />
            <View>暂无订单</View>
            </View>
          )}

{pageRef.current>1 && (
          <LoadMore
            style={{ paddingTop: 16, paddingBottom: 20 }}
            status={loadStatus}
          />
)
        }
        </View>
      </PullRefresh>

      {/* 底部弹出对话框 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />

      {/* 筛选抽屉 */}
      <FilterDrawer
        ref={filterDrawerRef}
        visible={isFilterDrawerVisible}
        onClose={handleFilterDrawerClose}
        onConfirm={handleFilterConfirm}
        onReset={handleFilterReset}
        onTimeClick={handleTimeClick}
        tempBeginDate={tempBeginDate}
        tempEndDate={tempEndDate}
      />

      {/* 日期选择器 - 使用Portal渲染到body顶层 */}
      {datePickerVisible && typeof document !== 'undefined' && createPortal(
        <DatePicker
          visible={datePickerVisible}
          title={currentDateType === 1 ? '选择起始时间' : '选择结束时间'}
          maskClosable
          disabled={false}
          currentTs={currentDateType === 1 ?
            (tempBeginDate !== '请选择' ? new Date(tempBeginDate).getTime() : Date.now()) :
            (tempEndDate !== '请选择' ? new Date(tempEndDate).getTime() : Date.now())
          }
          mode="date"
          onHide={handleDatePickerClose}
          onOk={(timestamp) => {
            const date = new Date(timestamp);
            const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            handleDateConfirm(formattedDate);
          }}
          formatter={(value, type) => {
            if (type === 'year') {
              return `${value}年`;
            } else if (type === 'month') {
              return `${value}月`;
            } else if (type === 'date') {
              return `${value}日`;
            }
            return `${value}`;
          }}
        />,
        document.body
      )}
    </View>
  );
}
