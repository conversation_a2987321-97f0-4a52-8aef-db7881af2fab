@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/sellOrder/details/index"] {
  .order-details-box {
    width: 100%;
    min-height: 100vh;
    background: #f7f8fa;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    padding-bottom: 80px;

    .order-status-bar-sell {
      background: var(--primary-color);
      color: #fff;
      font-size: 16px;
      padding: 12px 0 52px 16px;
      font-weight: bold;
    }

    .order-info-card-sell {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      padding: 0 10px;
    }

    .order-info-card-delivery {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      // padding:0 10px;
      padding-bottom: 16px;
    }

    .refund-quantity {
      color: #f53f3f;
      font-size: 12px;
    }

    .order-user-card-sell {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 0 16px 16px 16px;
      margin-top: -40px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    }

    .order-goods-card {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      padding: 16px;
    }

    // 新的订单信息行样式
    .order-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
    }

    .order-info-label {
      font-size: 14px;
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 80px;
      flex-shrink: 0;
    }

    .order-info-value {
      font-size: 14px;
      color: #333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      flex: 1;
    }

    .order-info-value-row {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: space-between;

      .order-info-value-row-left {
        display: flex;
        align-items: center;
        gap: 8px; // 图标之间的间距
      }
    }

    .copy-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      flex-shrink: 0;
    }

    .expand-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: transform 0.3s ease;
      flex-shrink: 0;

      &.expanded {
        transform: rotate(180deg);
      }
    }

    .expanded-time-info {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    // 联系人信息样式
    .contact-info-row {
      display: flex;
      padding: 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });

      &:last-child {
        border-bottom: none;
      }

      // 发件人行 - 图标和内容垂直居中
      &:first-child {
        align-items: center;
      }

      // 收件人行 - 图标顶对齐
      &:last-child {
        align-items: flex-start;
      }
    }

    .contact-info-remark-row {
      display: flex;
      flex-direction: column;
    }

    .contact-info-remark-row1 {
      display: flex;
      padding: 16px 16px 0 16px;
      align-items: center;
    }

    .contact-icon-wrapper {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      flex-shrink: 0;
      // display: flex;
      align-items: center;
      justify-content: center;
    }

    .contact-icon {
      display: block !important ;
      width: 20px;
      height: 20px;
    }

    .contact-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }

    .contact-details {
      flex: 1;
      display: flex;
      // align-items: center;
      gap: 8px;
    }

    .contact-details-receive {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .contact-name-row {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 2px;
    }

    .contact-name {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
    }

    .contact-address {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      line-height: 1.4;
      display: block;
    }

    .contact-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .contact-value {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      flex: 1;
      line-height: 1.4;
    }

    // 买家留言图片样式
    .remark-images-container {
      display: flex;
      align-items: flex-start;
      padding: 16px;
    }

    .contact-label {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 60px;
      flex-shrink: 0;
      margin-top: 0;
      font-weight: 500;
    }

    .remark-content-wrapper {
      flex: 1;
      margin-left: 12px;
    }

    .remark-images-scroll {
      display: flex;
      overflow-x: auto;
      gap: 8px;
      padding-bottom: 4px;
      margin-top: 8px;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .remark-image {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      flex-shrink: 0;
      object-fit: cover;
    }

    // 用户信息头部样式
    .user-info-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }

    .user-info-content {
      display: flex;
      align-items: center;
    }

    .user-name {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
    }

    // 商品卡片样式
    .shop-header {
      display: flex;
      align-items: center;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }

    .shop-name {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
      margin-right: 6px;
    }

    .verified-icon {
      width: 16px;
      height: 16px;
      margin-right: auto;
    }

    .shop-arrow {
      margin-left: auto;
    }

    .arrow-right {
      width: 16px;
      height: 16px;
    }

    // 使用订单列表的商品样式
    .goods-list-detail {
      // padding: 0 12px;
      padding-bottom: 10px;
    }

    .goods-item {
      display: flex;
      align-items: flex-start;
      margin-top: 12px;
    }



    .goods-img {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      background: #f5f5f5;
      margin-right: 10px;
    }

    .goods-info {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
    }
    
    .goods-info-detail {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
    }

    .goods-title-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .goods-title {
      flex: 1;
      font-size: 14px;
      color: #222;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-right: 8px;
      /* 显示两行，超出省略号 */
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .goods-price-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      flex-shrink: 0;
    }

    .goods-price {
      color: #f53f3f;
      font-weight: 600;
      font-size: 16px;
    }

    .goods-quantity {
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .goods-sku {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sku-count-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .sku-text {
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .sku-count {
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .sku-ems {
      font-size: 12px;
      color: var(--primary-color);
    }

    // 订单总计样式
    .order-summary-details {
      display: flex;
      flex-direction: column;
      align-items: space-between;
      padding-top: 10px;
      // padding: 16px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      &.total-row {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-top: 1px solid @dark-line-color;
      });
      }
    }

    .summary-label {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .summary-value {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .summary-total {
      font-size: 12px;
      color: #e35848;
    }

    .order-user-row {
      display: flex;
      align-items: center;
      padding: 16px 0 0 16px;
    }
    .order-user-name {
      font-size: 15px;
      font-weight: bold;
      color: @font-color;
    }
    .order-user-phone {
      font-size: 12px;
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-top: 2px;
    }

    .order-remark {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 0 16px 16px 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    }

    .order-img-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 0 12px 0;
      margin: 0 16px 16px 16px;
    }

    .order-goods-title {
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: bold;
      padding: 16px 0 0 16px;
    }
    .order-goods-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px 0 16px;
    }
    .order-goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .order-goods-name {
      font-size: 14px;
      font-weight: bold;
      color: @font-color;
      margin-bottom: 6px;
    }
    .order-goods-spec {
      font-size: 12px;
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-bottom: 8px;
    }
    .order-goods-price {
      font-size: 13px;
      color: #e35848;
      font-weight: bold;
    }
    .order-goods-summary {
      font-size: 13px;
      color: #333;
      padding: 0 16px 12px 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 12px 24px;
      .order-goods-actual {
        color: #e35848;
        font-weight: bold;
      }
    }

    .order-footer-bar-sell {
      height: 50px;
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
      box-shadow: 0 -2px 8px rgba(36, 104, 242, 0.03);
      padding: 10px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 10;
      .order-footer-bar-icon {
        width: 20px;
        height: 20px;
      }
      .order-footer-bar-btn {
        background: var(--primary-color);
        color: #fff;
        padding: 6px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }

      .order-info-btn-row {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .order-info-cancel-btn {
        border: 1px solid var(--primary-color);
        color: var(--primary-color);
        padding: 6px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
    }
  }

  // .arco-cell .cell-content .cell-text {
  //   text-align: left;
  //   color: #000;
  // }

  // .arco-cell .cell-label {
  //   margin-right: 10px;
  //   font-family: PingFang SC;
  //   font-size: 13px;
  //   font-weight: normal;
  //   line-height: 140%;
  //   text-align: justify; /* 浏览器可能不支持 */
  //   letter-spacing: normal;
  //   color: #86909c;
  // }

  // .cell-title
  // {
  //     width: 60px;
  // }

  .demo-cell-avatar-label {
    display: flex;
    align-items: center;
    .arco-avatar {
      .rem(width, 32);
      .rem(height, 32);
      .rem-with-rtl(margin-right, 8);
    }
  }
  .demo-cell-avatar {
    .cell-text {
      font-size: 0;
    }
    .arco-avatar {
      .rem(width, 24);
      .rem(height, 24);
      display: inline-block;
    }
  }

  .demo-cell-info {
    text-align: right;
    .info {
      .use-var(color, font-color);
      .rem(font-size, 16);
    }
    .sub-info {
      .use-var(color, cell-desc-color);
      .rem(font-size, 14);
    }
  }

  .info {
    font-size: 12px;
    line-height: 18px;
    padding-bottom: 16;
    width: 180px;
    margin-right: 35px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* 限制显示行数 */
    overflow: hidden;
    max-height: 6em; /* 可选，设置最大高度 */
  }

  .order-btn-detail {
    flex: 1;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border-right: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-right: 1px solid @dark-line-color;
  });
    background: #fff;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #86909c; // 默认灰色

    &:last-child {
      border-right: none;
    }

    // 所有普通按钮都是灰色
    &.normal-btn {
      color: #86909c;

      .btn-text {
        color: #86909c;
      }
    }

    // 只有立即支付按钮是蓝色
    &.pay-btn {
      .btn-text {
        color: var(--primary-color);
      }
    }
  }

  .btn-text {
    font-size: 14px;
    font-weight: 400;
    color: inherit; // 继承父元素颜色
  }

  .empty-order {
    text-align: center;
    color: #bbb;
    font-size: 15px;
    padding: 48px 0 32px 0;
  }

  .order-remark-header-detail {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
  }

  .order-remark-content {
    flex: 1;
    margin-right: 12px;
  }

  .order-remark-text {
    font-size: 15px;
    font-weight: 500;
    color: #222;
    line-height: 22px;
    margin-bottom: 4px;
    display: block;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });

    &:last-child {
      margin-bottom: 0;
    }
  }

  .order-remark-input-sell {
    width: 100%;
    flex: 1;
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });
    padding: 8px 0;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &::placeholder {
      color: #999;
      .use-dark-mode-query({
      color: #666;
    });
    }
  }

  .order-remark-camera {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  .camera-icon {
    width: 20px;
    height: 20px;
  }

  // 上传图片区域
  .uploaded-images-detail {
    display: flex;
    flex-wrap: nowrap; // 不换行
    gap: 8px;
    margin-top: 8px;
    overflow-x: auto; // 横向滚动
    overflow-y: hidden; // 禁止垂直滚动
    padding: 0 16px 4px 16px;
    height: 68px; // 固定高度，与图片高度(60px) + padding(4px) + gap(4px) 一致

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .uploaded-image-item {
    position: relative;
    width: 60px;
    height: 60px;
    flex-shrink: 0; // 防止图片被压缩
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
  }

  .delete-image-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 6px 0 0;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;

    &:active {
      opacity: 0.8;
    }
  }

  /* 发货弹窗样式 */
  .delivery-popup-overlay {
    position: fixed;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 60px; /* 距离底部按钮的距离 */
  }

  .delivery-popup-container {
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    border-radius: 6px;
    margin: 0 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .delivery-popup-content {
    .delivery-option {
      padding: 10px 28px;
      text-align: center;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });

      &:last-child {
        border-bottom: none;
      }

      .delivery-option-text {
        font-size: 13px;
        color: #333;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }
  }

  // 物流模块样式
  .logistics-card {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
    border-radius: 10px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    padding: 12px;
  }

  .logistics-card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .logistics-card-content-time {
      font-size: 14px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .logistics-card-content-status {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }
  }

  .logistics-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
  }

  .logistics-header {
    background: #f7f8fa;
    padding: 10px;
    border-radius: 4px;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    margin-bottom: 12px;
  }

  .logistics-status-row {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .logistics-status {
    font-size: 14px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    // 限制显示两行，超出显示省略号
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    max-height: 2.8em; // 2行的高度
    font-weight: 500;
  }

  .logistics-time {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-tracking-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0 0 0;
  }

  .logistics-tracking-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .logistics-tracking-btn {
    padding: 4px 16px;
    font-size: 12px;
    color: var(--primary-color);
    border-radius: 100px;
    border: 1px solid var(--primary-color);
  }

  .logistics-label {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-tracking-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .logistics-tracking-number {
    font-size: 12px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-goods-list {
    margin-bottom: 12px;
  }

  .logistics-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }

  .logistics-summary-text {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-summary-count {
    font-size: 12px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-weight: 500;
  }


}


  // 修改快递单号弹框样式
  .tracking-modal {
    .tracking-modal-content {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 16px 16px 0 0;
      padding: 0;
      max-height: 80vh;
    }

    .tracking-modal-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
      position: relative;

      .close-icon {
        position: absolute;
        left: 16px;
        width: 12px;
        height: 12px;
        cursor: pointer;
      }

      .tracking-modal-title {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }

    .tracking-modal-body {
      padding: 20px 16px;
    }

    .tracking-input-row {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .tracking-input-label {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 80px;
      flex-shrink: 0;
    }

    .tracking-input {
      flex: 1;
      padding: 0 12px;
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
      // background-color: @dark-card-background-color !important;
    });

      &::placeholder {
        color: #86909c;
      }
    }

    .tracking-select {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0 0 12px;
      cursor: pointer;
    //   .use-dark-mode-query({
    //   background-color: @dark-card-background-color !important;
    // });

      .tracking-select-text {
        font-size: 14px;
        color: #1d2129;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });

        &.placeholder {
          color: #86909c;
        }
      }

      .arrow-right {
        width: 16px;
        height: 16px;
      }
    }

    .tracking-modal-footer {
      padding: 16px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });

      .tracking-save-btn {
        width: 100%;
        height: 44px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }