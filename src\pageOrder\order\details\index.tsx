import { View, Text } from "@tarojs/components";
import { useDidShow, useLoad } from "@tarojs/taro";
import "./index.less";
import wx from "weixin-webview-jssdk";
import {
  NavBar,
  Image,
  Avatar,
  Divider,
  Tag,
  Loading,
  Dialog,
  Popup,
  Toast,
} from "@arco-design/mobile-react";
import React, { useState, useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import { toast } from "@/utils/yk-common";
import { IconRight,IconCopy,IconPhone,IconDownload,IconDown } from "@arco-iconbox/react-yk-arco";
import {
  getUserOrderDetails,
  getOrderPayParams,
  cancelOrder,
  updateOrderRemark,
  updateOrderCompleted,
  getDeliveryOrder,
} from "@/utils/api/common/common_user";
import { IconPayment } from "@/components/YkIcons";
import YkNavBar from "@/components/ykNavBar/index";
import ContactModal from "@/components/ContactModal";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
import areaDataType from '@/constants/area.json'; // 引入公有目录下的完整地区数据

export default function OrderDetails() {
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeBtn, setActiveBtn] = useState(-1); // -1表示无高亮按钮
  const [isExpanded, setIsExpanded] = useState(false); // 控制时间信息展开/收起
  const [logisticsData, setLogisticsData] = useState<any[]>([]);

  // 联系商家弹框相关状态
  const [showContactPopup, setShowContactPopup] = useState(false);

  const [platform,setPlatform] = useState<string>("H5");

  // 根据地区 ID 查找对应的名称
const findAreaNameById = (areaId: string): string => {
  for (const province of areaDataType) {
    if (province.id === areaId) {
      return province.name;
    }
    for (const city of province.children || []) {
      if (city.id === areaId) {
        return city.name;
      }

      for (const area of city.children || []) {
        if (area.id === areaId) {
          return `${province.name} ${city.name} ${area.name}`;
        }
      }
    }
  }
  return '';
};

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  useEffect(() => {
    //刷新订单事件
    Taro.eventCenter.on("refreshOrderList", fetchOrderDetails);
    //刷新物流数据事件
    Taro.eventCenter.on("refreshLogisticsData", fetchDeliveryOrder);

    // 使用全局回调管理器注册回调
    const callbackCleanup = useGlobalCallbacks('orderDetails', {
      webPaySuc: webPaySuc,
      wxPayWithStr: webPaySuc,
      aliPayWithStr: webPaySuc,
      // webDownloadSuc: webDownloadSuc,
      // webDownloadFail: webDownloadFail,
    });

    // 清理事件监听
    return () => {
      Taro.eventCenter.off("refreshOrderList", fetchOrderDetails);
      Taro.eventCenter.off("refreshLogisticsData", fetchDeliveryOrder);
      callbackCleanup && callbackCleanup();
    };
  }, []);

  // const webDownloadSuc = () => {
  //   toast("success", {
  //     content: "下载成功",
  //     duration: 2000,
  //   });
  // };

  // const webDownloadFail = () => {
  //   toast("error", {
  //     content: "下载失败",
  //     duration: 2000,
  //   });
  // };

  const webPaySuc = (status: number) => {
    console.log("webPaySuc", status);
    if (status === 1) {
      toast("success", {
        content: "支付成功",
        duration: 2000,
      });
      //刷新订单详情
      fetchOrderDetails();
    } else if (status === 2) {
      toast("error", {
        content: "支付失败",
        duration: 2000,
      });
    } else if (status === 3) {
      toast("error", {
        content: "支付取消",
        duration: 2000,
      });
    }
  };

  // 根据订单状态获取按钮列表
  const getOrderButtons = (status: string) => {
    console.log("status", status);
    switch (status) {
      case "待付款":
        return ["取消订单",  "联系商家", "立即支付"];
      case "已付款":
        return ["联系商家"];
      case "部分未发货":
        return ["联系商家", "再来一单"];
      case "全部已发货":
        return ["联系商家", "再来一单", "确认收货"];
      case "已完成":
        return ["联系商家", "再来一单"];
      case "退款":
        return ["退款详情", "再来一单"];
      case "退款中":
        return ["退款详情", "再来一单"];
      default:
        return ["联系商家"];
    }
  };

  const isFirstShowRef = useRef(true);
  useDidShow(() => {
    console.log("页面显示，检查是否需要刷新订单");
    // 第一次显示是页面初始化，不需要刷新（因为useEffect中已经加载了数据）
    if (isFirstShowRef.current) {
      isFirstShowRef.current = false;
      return;
    }
    
    // 微信小程序环境下，从其他页面返回时刷新订单列表
    // 这样可以确保从支付页面返回后能及时更新订单状态
    if (platform === "WX") {
      console.log("微信小程序环境：页面显示时刷新订单列表");
      fetchOrderDetails();
    }
  });
  
  // 处理按钮点击事件
  const handleButtonClick = (buttonText: string, index: number) => {
    setActiveBtn(index);

    switch (buttonText) {
      case "取消订单":
        // 处理取消订单逻辑
        handleCancelOrder();
        break;
      case "联系商家":
        // 处理联系商家逻辑
        console.log("联系商家", orderDetails);
         // 使用内置联系弹窗
      if (!orderDetails?.wechatNumber && !orderDetails?.userPhone&&!orderDetails?.wechatQrCode) {
        toast("info", {
          content: "暂无联系方式",
          duration: 2000,
        });
        return;
      }
        setShowContactPopup(true);
        break;
      case "立即支付":
        // 处理立即支付逻辑
        handlePayment();
        break;
      case "再来一单":
        // 处理再来一单逻辑
        handleReorder();
        break;
      case "退款详情":
        // 处理退款详情逻辑
        console.log("退款详情");
        handleRefundDetailClick();
        break;
      case "确认收货":
        // 处理确认收货逻辑
        console.log("确认收货");
        handleReceiveOrder();
        break;
      default:
        break;
    }
  };

  // 处理支付逻辑
  const handlePayment = async () => {
    try {
      const orderId = getOrderId();
      if (!orderId) {
        toast("error", {
          content: "订单ID不存在",
          duration: 2000,
        });
        return;
      }

      // 获取支付参数
      const payParamsResponse = await getOrderPayParams({ orderId: orderId, channelCode: platform === "WX" ? "wx_lite" : "wx_app" });

      if (payParamsResponse && payParamsResponse.code === 0) {
        const payParams = payParamsResponse.data;
        console.log("支付参数:", payParams);
        if(platform === "HM"){
          window.harmony.wxPay(JSON.stringify(payParams));
        }else if(platform === "IOS"){
          window.webkit.messageHandlers.wxPayWithStr.postMessage(JSON.stringify(payParams));
        }else if(platform === "Android"){
          window.wxPay.wxPay(JSON.stringify(payParams));
        } else if (platform === "WX") {
          payParams.userOrderId = orderId

          wx.miniProgram.navigateTo({
            url: '/pages/wxpay/index?payParams=' + encodeURIComponent(JSON.stringify(payParams))
          });
        } 
      } else {
        toast("error", {
          content: payParamsResponse.msg || "获取支付信息失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("支付处理失败:", error);
      toast("error", {
        content: "支付处理失败",
        duration: 2000,
      });
    }
  };

  // 处理取消订单（参考订单列表实现）
  const handleCancelOrder = async () => {
    try {
      const orderId = getOrderId();
      if (!orderId) {
        toast("error", {
          content: "订单ID不存在",
          duration: 2000,
        });
        return;
      }

      Dialog.confirm({
        title: "确认取消？",
        children: "取消后订单将会失效且无法恢复,请谨慎操作!",
        okText: "确定",
        cancelText: "取消",
        platform: "ios",
        onOk: async () => {
          // 显示加载提示
          Taro.showLoading({
            title: "正在取消订单...",
          });

          // 调用取消订单接口
          const cancelResult = await cancelOrder({ id: orderId });

          Taro.hideLoading();

          if (cancelResult && cancelResult.code === 0) {
            // 取消成功，显示成功提示
            toast("success", {
              content: "订单已取消",
              duration: 2000,
            });

            // 刷新订单详情
            fetchOrderDetails();
          } else {
            // 取消失败，显示错误信息
            toast("error", {
              content: cancelResult.msg || "取消订单失败，请重试",
              duration: 2000,
            });
          }
        },
      });
    } catch (error) {
      Taro.hideLoading();
      console.error("取消订单失败:", error);
      toast("error", {
        content: "取消订单失败，请检查网络连接",
        duration: 2000,
      });
    }
  };

    // 处理退款详情按钮点击
    const handleRefundDetailClick = () => {
      Taro.navigateTo({
        url: `/pageOrder/refundList/index?orderId=${orderDetails.id}&orderType=buyer`,
      });
    };

  const handleReceiveOrder = async () => {
    try {
      const orderId = getOrderId();
      if (!orderId) {
        toast("info", {
        content: "订单ID不存在",
        duration: 2000
      });
        return;
      }

      Dialog.confirm({
        title: "确认收货？",
        children: "确认收货后订单将会失效且无法恢复,请谨慎操作!",
        okText: "确定",
        cancelText: "取消",
        platform: "ios",
        onOk: async () => {
          try {
            const receiveResult = await updateOrderCompleted({
              id: orderId,
              // orderType: "4",
            });
            if (receiveResult && receiveResult.code === 0) {
              toast("success", {
        content: "确认收货成功",
        duration: 2000
      });
              // 刷新订单详情
              fetchOrderDetails();
            } else {
              toast("error", {
                content: receiveResult.msg || "确认收货失败",
                duration: 2000,
              });
            }
          } catch (error) {
            console.error("确认收货失败:", error);
            toast("info", {
        content: "确认收货失败",
        duration: 2000
      });
          }
        },
      });
    } catch (error) {
      console.error("确认收货失败:", error);
      toast("info", {
        content: "确认收货失败",
        duration: 2000
      });
    }
  };

  // 获取路由参数中的订单ID
  const getOrderId = () => {
    const router = Taro.getCurrentInstance().router;
    return router?.params?.id || router?.params?.orderId;
  };

  // 联系商家弹框相关函数
  const closeContactPopup = () => {
    setShowContactPopup(false);
  };

  const fetchDeliveryOrder = async () => {
    const orderId = getOrderId();
    if (!orderId) {
      toast("info", {
        content: "订单ID不存在",
        duration: 2000
      });
      return;
    }

    try {
      const response = await getDeliveryOrder({ orderId: orderId });

      if (response && response.code === 0 && response.data && response.data.list) {
        setLogisticsData(response.data.list);
      } else {
        toast("error", {
          content: response.msg || "获取物流信息失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取物流信息失败:", error);
      toast("info", {
        content: "获取物流信息失败",
        duration: 2000
      });
    }
  };

  // 处理再来一单逻辑
  const handleReorder = () => {
    if (!orderDetails) {
      toast("error", {
        content: "订单信息不存在",
        duration: 2000
      });
      return;
    }

    console.log("再来一单订单详情数据:", orderDetails);

    // 从订单详情中提取商品信息
    const userOrderDynamic = orderDetails.orderDynamics?.[0];
    if (!userOrderDynamic) {
      toast("error", {
        content: "订单商品信息不存在",
        duration: 2000
      });
      return;
    }

    // 构建details数组，从orderDetails中提取SKU信息
    const details = userOrderDynamic.orderDetails?.map((orderDetail: any) => ({
      id: Date.now() + Math.random(),
      userId: orderDetails.userId,
      shoppingCartId: Date.now() + Math.random(),
      productColorId: orderDetail.productColorId || null, // 订单详情中没有colorId，需要根据colorName查找
      productSpecificationsId: orderDetail.productSpecificationsId || null, // 订单详情中没有specId，需要根据specName查找
      productSpecificationsName: orderDetail.specName || null,
      productColorName: orderDetail.colorName || null,
      quantity: orderDetail.quantity || 1,
      price: userOrderDynamic.price || 0,
      checked: true
    })) || [{
      id: Date.now() + Math.random(),
      userId: orderDetails.userId,
      shoppingCartId: Date.now() + Math.random(),
      productColorId: null,
      productSpecificationsId: null,
      productSpecificationsName: null,
      productColorName: null,
      quantity: 1,
      price: userOrderDynamic.price || 0,
      checked: true
    }];

    // 构建selectedItems数据结构
    const selectedItems = [{
      shoppingCartList: [{
        id: Date.now(),
        shoppingCartSellerId: orderDetails.sellerUserId,
        dynamicsId: userOrderDynamic.dynamicId,
        dynamicsContent: userOrderDynamic.dynamicName || '',
        dynamicsImage: userOrderDynamic.dynamicPictures || '',
        remark: '', // 再来一单时备注为空
        type: 1,
        price: userOrderDynamic.price || 0,
        checked: true,
        details: details,
        isCollect: 0
      }],
      shoppingCartSellerId: orderDetails.sellerUserId,
      shoppingCartSellerUserId: orderDetails.sellerUserId,
      dynamicsUserName: orderDetails.dynamicUserName || '',
      dynamicsUserAvatar: orderDetails.dynamicUserAvatar || '',
      checked: true
    }];

    console.log("构建的selectedItems:", selectedItems);

    // 存储到本地存储
    Taro.setStorageSync('selectedItems', selectedItems);

    // 跳转到确认订单页面
    Taro.navigateTo({
      url: '/pageOrder/order/pay/index?from=detail'
    });
  };

  // 获取订单详情
  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const orderId = getOrderId();

      if (!orderId) {
        toast("info", {
        content: "订单ID不存在",
        duration: 2000
      });
        return;
      }

      const response = await getUserOrderDetails({ id: orderId });

      if (response && response.code === 0) {
        setOrderDetails(response.data);
      } else {
        toast("error", {
          content: response.msg || "获取订单详情失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
      toast("info", {
        content: "获取订单详情失败",
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    fetchOrderDetails();
    fetchDeliveryOrder();
  });

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <View className="order-details-box">
       {platform !== "WX" && <YkNavBar title="订单详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Loading />
          <Text style={{ marginTop: "10px", display: "block" }}>加载中...</Text>
        </View>
      </View>
    );
  }

  // 如果没有订单数据，显示错误状态
  if (!orderDetails) {
    return (
      <View className="order-details-box">
        {platform !== "WX" &&<YkNavBar title="订单详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Text>订单详情不存在</Text>
        </View>
      </View>
    );
  }
  // 格式化时间
  const formatTime = (timestamp: number) => {
    if (!timestamp) return "--";
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 获取订单状态文本
  const getOrderStatusText = (orderType: number) => {
    switch (orderType) {
      case 1:
        return "待付款";
      case 2:
        return "已付款";
      case 3:
        if (orderDetails.shippingStatus === 1) {
          return "全部已发货";
        } else {
          return "部分未发货";
        }
      case 4:
        return "已完成";
      case 5:
        return "退款中";
      case 6:
        return "已退款";
      case 7:
        return "已取消";
      default:
        return "未知状态";
    }
  };

  // 复制订单号
  const handleCopyOrderNo = (orderNo: string) => {
    Taro.setClipboardData({
      data: orderNo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "订单号已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 切换展开/收起状态
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // 复制发件人信息
  const handleCopySender = (senderInfo: string) => {
    Taro.setClipboardData({
      data: senderInfo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "发件人信息已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 复制收件人信息
  const handleCopyReceiver = (receiverInfo: string) => {
    Taro.setClipboardData({
      data: receiverInfo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "收件人信息已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 复制快递单号
  const handleCopyTrackingNumber = (trackingNumber: string) => {
    Taro.setClipboardData({
      data: trackingNumber,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "快递单号已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 获取物流数据（使用真实接口数据）
  const getLogisticsData = () => {
    if (!logisticsData || logisticsData.length === 0) {
      return [];
    }

    return logisticsData.map((logistics: any) => {
      // 获取物流状态
      const status = logistics.trackResult 
        ? logistics.trackResult.context
        : "查无物流信息";

      // 获取更新时间
      const updateTime = logistics.trackResult
        ? logistics.trackResult.ftime
        : new Date(logistics.createTime).toLocaleString();

      // 通过 shippingDetails 中的 dynamicId 获取商品信息
      const goods: any[] = [];
      let totalQuantity = 0;

      if (logistics.dynamic && orderDetails?.orderDynamics) {
        logistics.dynamic.forEach((shippingDetail: any) => {
          // 查找对应的 dynamics（注意：这里应该用 dynamicId 匹配 dynamics.dynamicsId）
          const dynamics = orderDetails.orderDynamics.find(
            (d: any) => d.dynamicId === shippingDetail.dynamicId
          );
          console.log('dynamics11111111111', dynamics);
          console.log('shippingDetail11111111111', shippingDetail);

          if (dynamics) {
            // 遍历发货详情数组
            shippingDetail.shipmentDetails?.forEach((shipmentDetail: any) => {
              // 查找对应的 orderDetail 来获取规格信息
              const orderDetail = dynamics.orderDetails?.find(
                (od: any) => od.id === shipmentDetail.orderDetailId
              );
          
              console.log('orderDetail11111111111', orderDetail);
              
              let spec = "";
              if (orderDetail && orderDetail.properties) {
                try {
                  // 解析 properties JSON 字符串
                  const properties = JSON.parse(orderDetail.properties);
                  // 格式化显示颜色和规格
                  spec = `${properties.colors || ""} ${properties.specifications || ""}`.trim();
                } catch (error) {
                  console.error('解析 properties 失败:', error);
                  spec = orderDetail.properties; // 如果解析失败，直接显示原始字符串
                }
              }
          
              goods.push({
                image: dynamics.dynamicPictures || "",
                title: dynamics.dynamicName || "商品名称",
                price: dynamics.price || "0",
                spec: spec,
                quantity: orderDetail.quantity || 0,
                quantityShipped: shipmentDetail.quantity || 0,
              });
              
              totalQuantity += shipmentDetail.quantity || 0;
            });
            
            console.log('goods11111111111', goods);
          }
        });
      }

      return {
        id: logistics.id,
        status: status,
        updateTime: updateTime,
        trackingNumber: logistics.trackingNumber,
        deliveryCompany: logistics.expressCompany,
        goods: goods,
        totalQuantity: totalQuantity,
      };
    });
  };

  return (
    <View className="order-details-box">
      {platform !== "WX" &&<YkNavBar title="订单详情" />}
      <View className="order-status-bar">
        {getOrderStatusText(orderDetails.status)}
      </View>
      <View className="order-info-card">
        {/* 订单编号行 - 带复制和展开/收起功能 */}
        <View className="order-info-row">
          <Text className="order-info-label">订单编号</Text>
          <View className="order-info-value-row">
            <View className="order-info-value-row-left">
              <Text className="order-info-value">
                {orderDetails.combineOutTradeNo || orderDetails.id || "--"}
              </Text>
              {/* <Image
                className="copy-icon"
                src={require("@/assets/images/common/copy_icon.png")}
                onClick={() =>
                  handleCopyOrderNo(
                    orderDetails.combineOutTradeNo || orderDetails.id || ""
                  )
                }
              /> */}
              <IconCopy
                className="copy-icon"
                onClick={() =>
                  handleCopyOrderNo(
                    orderDetails.combineOutTradeNo || orderDetails.id || ""
                  )
                }
              />
            </View>
            {/* <Image
              className={`expand-icon ${isExpanded ? "expanded" : ""}`}
              src={require("@/assets/images/common/arrow_down.png")}
              onClick={toggleExpanded}
            /> */}

            <IconDown
              className={`expand-icon ${isExpanded ? "expanded" : ""}`}
              onClick={toggleExpanded}
            />
          </View>
        </View>

        {/* 可展开/收起的时间信息 */}
        {isExpanded && (
          <View className="expanded-time-info">
            <View className="order-info-row">
              <Text className="order-info-label">开单时间</Text>
              <Text className="order-info-value">
                {formatTime(orderDetails.createTime)}
              </Text>
            </View>
            {orderDetails.successTime && (
              <View className="order-info-row">
                <Text className="order-info-label">付款时间</Text>
                <Text className="order-info-value">
                  {formatTime(orderDetails.successTime) || "--"}
                </Text>
              </View>
            )}
            {orderDetails.cancelTime && orderDetails.status == 7 && (
              <View className="order-info-row">
                <Text className="order-info-label">取消时间</Text>
                <Text className="order-info-value">
                  {formatTime(orderDetails.cancelTime) || "--"}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      <View className="order-user-card">
        {/* 用户信息 */}
        <View className="user-info-header" onClick={(e) =>{
          e.stopPropagation();
          if(orderDetails.userId === Taro.getStorageSync("userInfo").userId){
            Taro.navigateTo({
              url: `/pageDynamic/album/index`,
            })
          }else{
          Taro.navigateTo({
              url: `/pageUserInfo/userDetail/index?userId=${orderDetails.userId}`,
            })
          }
        }}>
          <View className="user-info-content">
            <Avatar
              size="small"
              style={{ marginRight: 8 }}
              src={Taro.getStorageSync("userInfo").avatar || ""}
            />
            <Text className="user-name">
              {Taro.getStorageSync("userInfo").nickname || ""}
            </Text>
          </View>
          {/* <Image
            className="arrow-right"
            src={require("@/assets/images/common/arrow_right.png")}
          /> */}
          <IconRight
            className="arrow-right"
          />
        </View>

        {/* 发件人信息 */}
        <View className="contact-info-row">
          <Text className="contact-label">发件人</Text>
          <View className="contact-info">
            <View className="contact-details">
              <Text className="contact-name">
                {orderDetails.customizeSenderName ||
                  orderDetails.userName ||
                  ""}{" "}
                {orderDetails.customizeSenderPhone ||
                  orderDetails.userPhone ||
                  ""}
              </Text>
              {/* <Text className="contact-phone"></Text> */}
            </View>
            {/* <Image
              className="copy-icon"
              src={require("@/assets/images/common/copy_icon.png")}
              onClick={() =>
                handleCopySender(
                  `${
                    orderDetails.customizeSenderName ||
                    orderDetails.dynamicUserName ||
                    ""
                  } ${
                    orderDetails.customizeSenderPhone ||
                    orderDetails.dynamicUserPhone ||
                    ""
                  }`
                )
              }
            /> */}
            <IconCopy
              className="copy-icon"
              onClick={() =>
                handleCopySender(
                  `${
                    orderDetails.customizeSenderName ||
                    orderDetails.userName ||
                    ""
                  } ${
                    orderDetails.customizeSenderPhone ||
                    orderDetails.userPhone ||
                    ""
                  }`
                )
              }
            />
          </View>
        </View>

        {/* 收件人信息 */}
        <View className="contact-info-row">
          <Text className="contact-label">收件人</Text>
          <View className="contact-info">
            <View className="contact-details">
              <Text className="contact-name">
                {orderDetails.receiverName || ""}{" "}
                {orderDetails.receiverMobile || ""}
              </Text>
              {/* <Text className="contact-phone">{orderDetails.receivingPhone ||  ''}</Text> */}
              <Text className="contact-address">
                {findAreaNameById(orderDetails.receiverAreaId) || ""}{" "}
                {orderDetails.receiverDetailAddress || ""}
              </Text>
            </View>
          
            <IconCopy
              className="copy-icon"
              onClick={() =>
                handleCopyReceiver(
                  `${orderDetails.receiverName || ""} ${
                    orderDetails.receiverMobile ||
                    orderDetails.userPhone ||
                    ""
                  } ${findAreaNameById(orderDetails.receiverAreaId)} ${
                    orderDetails.receiverDetailAddress || ""
                  }`
                )
              }
            />
          </View>
        </View>

        {/* 配送方式 */}
        <View className="contact-info-row">
          <Text className="contact-label">配送方式</Text>
          <View className="contact-info">
            <Text className="contact-value">
              {orderDetails.deliveryName || "--"}
            </Text>
          </View>
        </View>

        {/* 支付方式 */}
        <View className="contact-info-row">
          <Text className="contact-label">支付方式</Text>
          <View className="contact-info">
            <Text className="contact-value">
              微信
            </Text>
          </View>
        </View>

        {/* 运费 */}
        <View className="contact-info-row">
          <Text className="contact-label">运费</Text>
          <View className="contact-info">
            <Text className="contact-value">
              ¥{(orderDetails.deliveryPrice/100).toFixed(2) || 0}
            </Text>
          </View>
        </View>
      </View>

      {orderDetails.pictureRemark || orderDetails.remark ? (
        <View className="order-remark">
          {/* 买家留言 */}
          <View className="contact-info-row">
            <Text className="contact-label">我的留言</Text>
            <View className="contact-info">
              <Text className="contact-value">
                {orderDetails.remark || "--"}
              </Text>
            </View>
          </View>

          {/* 买家留言图片 - 横向滚动 */}
          {orderDetails.pictureRemark && (
            <View className="remark-images-container">
              <View className="remark-images-scroll">
                {orderDetails.pictureRemark
                  .split(",")
                  .map((img: string, idx: number) => (
                    <Image
                      key={idx}
                      src={img.trim()}
                      className="remark-image"
                    />
                  ))}
              </View>
            </View>
          )}
        </View>
      ) : null}

      <View className="order-goods-card">
        {/* 商家信息 */}
        <View className="shop-header" onClick={(e) =>{
          e.stopPropagation();
          if(orderDetails.sellerUserId === Taro.getStorageSync("userInfo").userId){
            Taro.navigateTo({
              url: `/pageDynamic/album/index`,
            })
          }else{
            Taro.navigateTo({
              url: `/pageUserInfo/userDetail/index?userId=${orderDetails.sellerUserId}`,
            })
          }
        }}>
          <Avatar
            size="small"
            style={{ marginRight: 8 }}
            src={orderDetails.userAvatar || ""}
          />
          <Text className="shop-name">
            {orderDetails.userName || ""}
          </Text>
          {/* <Image
            className="verified-icon"
            src={require("@/assets/images/common/wx_pay.png")}
          /> */}

          <IconPayment className="verified-icon" />

          <View className="shop-arrow">
            {/* <Image
              className="arrow-right"
              src={require("@/assets/images/common/arrow_right.png")}
            /> */}
            <IconRight
              className="arrow-right"
            />
          </View>
        </View>

        {/* 商品列表 */}
        {orderDetails.orderDynamics &&
          orderDetails.orderDynamics.length > 0 && (
            <View className="goods-list-detail">
              {orderDetails.orderDynamics.map(
                (dynamic: any, index: number) => {
                  const firstImage = dynamic.dynamicPictures
                    ? dynamic.dynamicPictures.split(",")[0]
                    : "";
                  return (
                    <View key={index} className="goods-item">
                      <Image src={firstImage} className="goods-img" />
                      <View className="goods-info">
                        <View className="goods-title-row">
                          <Text className="goods-title">
                            {dynamic.dynamicName || "--"}
                          </Text>
                          <View className="goods-price-section">
                            <Text className="goods-price">
                              ￥{(dynamic.price/100).toFixed(2) || 0}
                            </Text>
                            <Text className="goods-quantity">
                              x{dynamic.count || 1}
                            </Text>
                          </View>
                        </View>
                        {/* 显示SKU信息 */}
                        {dynamic.orderDetails &&
                          dynamic.orderDetails.map(
                            (detail: any, detailIdx: number) => (
                              <View className="goods-sku" key={detailIdx}>
                               <Text className="sku-text">
  {(() => {
    try {
      const props = JSON.parse(detail.properties || "{}");
      return `${props.colors || ""} ${props.specifications || ""}`.trim();
    } catch (e) {
      return detail.properties; // 如果解析失败，就原样显示
    }
  })()}
</Text>
                                <View className="sku-count-wrapper">
                                  {detail.refundQuantity > 0 && (
                                    <Text className="refund-quantity">{`退款x${detail.refundQuantity}`}</Text>
                                  )}
                                  <Text className="sku-count">
                                    {`x${detail.quantity} ${
                                      orderDetails.status === 3
                                        ? `(未发货${
                                            detail.quantity -
                                            detail.quantityShipped
                                          })`
                                        : ""
                                    }`}
                                  </Text>

                                  {orderDetails.status === 3 && (
                                    <Text
                                      className="sku-ems"
                                      onClick={() => {
                                        const skuImage = dynamic.dynamicPictures
                                          ? dynamic.dynamicPictures.split(",")[0]
                                          : "";
                                        const goodsInfo = {
                                          image: skuImage,
                                          title: dynamic.dynamicName || "商品名称",
                                          price: dynamic.price || "0",
                                          // spec: `${detail.colorName || ""} ${
                                          //   detail.specName || ""
                                          // }`.trim(),
                                          spec: detail.properties,
                                          quantity: detail.quantity || 0,
                                          quantityShipped:
                                            detail.quantityShipped || 0,
                                          unshippedQuantity:
                                            (detail.quantity || 0) -
                                            (detail.quantityShipped || 0),
                                          receivingAddress: `${
                                            findAreaNameById(orderDetails.receiverAreaId) || ""
                                          }${
                                            orderDetails?.receiverDetailAddress || ""
                                          }`,
                                        };

                                        Taro.navigateTo({
                                          url: `/pageOrder/skuEms/index?orderId=${
                                            orderDetails.id
                                          }&id=${
                                            detail.orderDetailId
                                          }&goodsInfo=${encodeURIComponent(
                                            JSON.stringify(goodsInfo)
                                          )}`,
                                        });
                                      }}
                                    >
                                      查看物流
                                    </Text>
                                  )}
                                </View>
                              </View>
                            )
                          )}
                      </View>
                    </View>
                  );
                }
              )}
            </View>
          )}

        {/* 订单总计 */}
        <View className="order-summary-details">
          <View className="summary-row">
            <Text className="summary-label">商品数量</Text>
            <Text className="summary-value">
              {/* {orderDetails.orderDynamics?.reduce(
                (total: number, dynamic: any) => total + (dynamic.number || 0),
                0
              ) || 0} */}

{orderDetails.orderDynamics[0].count || 0}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">商品总金额</Text>
            <Text className="summary-value">
              ¥{(orderDetails.totalPrice/100).toFixed(2) || 0}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">运费</Text>
            <Text className="summary-value">
              ¥{(orderDetails.deliveryPrice/100 || 0).toFixed(2)}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">实付金额</Text>
            <Text className="summary-value">
              ¥
              {(orderDetails.totalPrice/100 + orderDetails.deliveryPrice/100).toFixed(
                2
              ) || 0}
            </Text>
          </View>

          {orderDetails.orderRefund &&
            orderDetails.orderRefund.refundAmount > 0 && (
              <View className="summary-row">
                <Text className="summary-label">退款金额</Text>
                <Text className="summary-total">
                  ¥
                  {(
                    orderDetails.orderRefund?.refundAmount +
                      orderDetails.orderRefund?.refundPostage || 0
                  ).toFixed(2) || 0}
                </Text>
              </View>
            )}
        </View>

        {/* <View className="order-goods-summary">
          <View>商品数量：{order.goods[0].count}</View>
          <View>商品总金额：¥{order.total}</View>
          <View>运费：¥{order.freight}</View>
          <View className="order-goods-actual">实付金额：<Text style={{ color: '#e35848' }}>¥{order.actual}</Text></View>
        </View> */}
      </View>

      {/* 物流模块 */}
      {getLogisticsData().map((logistics: any, index: number) => (
        <View key={index} className="logistics-card">
          <View className="logistics-card-content">
            <Text className="logistics-card-content-time">
              {logistics.updateTime}
            </Text>
            <Text className="logistics-card-content-status">已发货</Text>
          </View>
          {/* 物流信息头部 */}
          <View className="logistics-header">
            <View
              className="logistics-header-content"
              onClick={() => {
                const receivingAddress = `${findAreaNameById(orderDetails.receiverAreaId)} ${
                  orderDetails?.receiverDetailAddress || ""
                }`;

                Taro.navigateTo({
                  url: `/pageOrder/emsDetail/index?trackingNumber=${encodeURIComponent(
                    logistics.trackingNumber
                  )}&expressCompany=${encodeURIComponent(
                    logistics.deliveryCompany
                  )}&receivingAddress=${encodeURIComponent(receivingAddress)}`,
                });
              }}
            >
              <View className="logistics-status-row">
                <Text className="logistics-status">{logistics.status}</Text>
                <Text className="logistics-time">{logistics.updateTime}</Text>
              </View>
              {/* <Image
                className="arrow-right"
                src={require("@/assets/images/common/arrow_right.png")}
              /> */} 
              <IconRight
                className="arrow-right"
              />
            </View>

            {/* 快递单号 */}
            <View className="logistics-tracking-row">
              <Text className="logistics-label">
                {logistics.deliveryCompany}单号
              </Text>
              <View className="logistics-tracking-info">
                <Text className="logistics-tracking-number">
                  {logistics.trackingNumber}
                </Text>
                {/* <Image
                  className="copy-icon"
                  src={require("@/assets/images/common/copy_icon.png")}
                  onClick={() =>
                    handleCopyTrackingNumber(logistics.trackingNumber)
                  }
                /> */}
                <IconCopy
                  className="copy-icon"
                  onClick={() =>
                    handleCopyTrackingNumber(logistics.trackingNumber)
                  }
                />
              </View>
            </View>
          </View>

          {/* 物流商品列表 */}
          <View className="logistics-goods-list">
            {logistics.goods.map((item: any, goodsIndex: number) => (
              <View key={goodsIndex} className="goods-item">
                <Image src={item.image} className="goods-img" />
                <View className="goods-info">
                  <View className="goods-title-row">
                    <Text className="goods-title">{item.title}</Text>
                    <View className="goods-price-section">
                      <Text className="goods-price">¥{item.price}</Text>
                      <Text className="goods-quantity">x{item.quantity}</Text>
                    </View>
                  </View>
                  <View className="goods-sku">
                    <Text className="sku-text">{item.spec}</Text>
                    <Text className="sku-count">x{item.quantityShipped}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>

          {/* 已发数量 */}
          <View className="logistics-summary">
            <Text className="logistics-summary-text">已发数量</Text>
            <Text className="logistics-summary-count">
              {logistics.totalQuantity}
            </Text>
          </View>
        </View>
      ))}

      <View className="order-footer-bar">
        {getOrderButtons(getOrderStatusText(orderDetails.status) || "").map(
          (btn, i) => (
            <View
              className={`order-btn-detail ${
                btn === "立即支付" ? "pay-btn" : "normal-btn"
              }`}
              key={btn}
              onClick={() => handleButtonClick(btn, i)}
            >
              <Text className="btn-text">{btn}</Text>
            </View>
          )
        )}
      </View>

      {/* 联系商家弹框 */}
      <ContactModal
        visible={showContactPopup}
        onClose={closeContactPopup}
        contactInfo={{
          wechatQrCode: orderDetails?.wechatQrCode,
          wechatNumber: orderDetails?.wechatNumber,
          contactMobile: orderDetails?.userPhone,
          nickname: orderDetails?.userName
        }}
        // onDownloadQrCode={handleDownloadQrCode}
        // onCopyWechat={handleCopyWechat}
        // onCopyPhone={handleCopyPhone}
        // onCallPhone={handleCallPhone}
        // onChat={handleChat}
      />
    </View>
  );
}
