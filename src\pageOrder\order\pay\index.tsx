import React, { useState } from "react";
import { View, Image, Text, Input } from "@tarojs/components";
import {
  <PERSON><PERSON>,
  <PERSON>per,
  Cell,
  Avatar,
  Switch,
  Picker,
} from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar";
import "./index.less";
import Taro from "@tarojs/taro";
import { useDidShow } from "@tarojs/taro";
import { useEffect, useRef } from "react";
import {
  createOrder,
  getOrderPayParams,
  getDeliveryList,
  getDeliveryTemplateList,
  getReceivingAddress,
  uploadFile,
  getFreight,
} from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";
import { AuthTypes } from "@/utils/config/authTypes";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
import wx from "weixin-webview-jssdk";
// 声明 window.wxPay 类型
declare global {
  interface Window {
    wxPay?: {
      wxPay: (params: string) => void;
    };
  }
}

interface AddressInfo {
  id?: number;
  name?: string;
  mobile?: string;
  area?: string;
  areaId?: number;
  areaName?: string;
  detailAddress?: string;
}

interface CartDetail {
  id: number;
  userId: number;
  shoppingCartId: number;
  productColorId: number | null;
  productSpecificationsId: number | null;
  productSpecificationsName: string | null;
  productColorName: string | null;
  quantity: number;
  price: number;
  checked: boolean;
}

interface CartItem {
  id: number;
  shoppingCartSellerId: number;
  dynamicsId: number;
  dynamicsContent: string;
  dynamicsImage: string;
  remark: string;
  type: number;
  price: number;
  checked: boolean;
  details: CartDetail[];
  isCollect: number;
}

interface SellerCart {
  shoppingCartList: CartItem[];
  shoppingCartSellerId: number;
  shoppingCartSellerUserId: number;
  dynamicsUserName: string;
  dynamicsUserAvatar: string;
  checked: boolean;
}

export default function PayOrderPage() {
  const [customSender, setCustomSender] = useState(false);
  const [senderName, setSenderName] = useState("");
  const [senderPhone, setSenderPhone] = useState("");
  const [address, setAddress] = useState<AddressInfo>({});
  const [selectedItems, setSelectedItems] = useState<SellerCart[]>([]);
  const [deliveryOptions, setDeliveryOptions] = useState<
    Array<{ label: string; value: string; orderReminder?: string }>
  >([]);
  const [freight, setFreight] = useState<number>(0); // 运费状态
  const [freightCalculated, setFreightCalculated] = useState<boolean>(false); // 运费是否计算成功
  const [isFromCart, setIsFromCart] = useState(true); // 是否从购物车进入，默认true

  const orderId = useRef("");
  const errMsg = useRef("");
  // 上传的图片列表
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  // 下单加载状态
  const [orderLoading, setOrderLoading] = useState(false);
  // 统一留言状态
  const [globalRemark, setGlobalRemark] = useState("");

  // 图片选择弹窗状态
  const [isPopupVisible, setPopupVisible] = useState(false);
  const chooseImageRef = useRef<"album" | "camera">("album");

  // 选择图片的方法
  const chooseImage = (sourceType: "album" | "camera") => {
    const count = 9 - uploadedImages.length;
    if (platformRef.current === "Android") {
      window.setPhotoNum?.setPhotoNum(count);
    }
    if (platformRef.current === "HM") {
      window.harmony.setPhotoNum(count);
    }
    Taro.chooseImage({
      count,
      sizeType: ["original", "compressed"],
      sourceType: [sourceType],
      success: async (res) => {
        console.log("选择的图片:", res.tempFilePaths);

        // 显示上传进度
        Taro.showLoading({
          title: "上传中...",
        });

        try {
          // 上传所有选择的图片
          const uploadPromises = res.tempFilePaths.map(async (imagePath) => {
            const uploadRes = await uploadFile([imagePath]);
            console.log("上传结果:", uploadRes);

            // 根据实际返回结构提取URL
            let url = "";
            if (uploadRes && uploadRes.code === 0) {
              if (typeof uploadRes.data === "string") {
                url = uploadRes.data;
              } else if (uploadRes.data && uploadRes.data.url) {
                url = uploadRes.data.url;
              } else if (Array.isArray(uploadRes.data) && uploadRes.data[0]) {
                url = uploadRes.data[0];
              }
            }

            if (!url) {
              throw new Error("上传失败，未获取到图片URL");
            }

            return url;
          });

          const uploadedUrls = await Promise.all(uploadPromises);
          console.log("所有图片上传完成:", uploadedUrls);

          // 更新上传的图片列表
          setUploadedImages((prev) => [...prev, ...uploadedUrls]);

          Taro.hideLoading();
          toast("success", {
            content: "上传成功",
            duration: 2000,
          });
        } catch (error) {
          console.error("上传图片失败:", error);
          Taro.hideLoading();
          toast("error", {
            content: "上传失败，请重试",
            duration: 2000,
          });
        }
      },
      fail: (error) => {
        console.error("选择图片失败:", error);
        toast("error", {
          content: "选择图片失败",
          duration: 2000,
        });
      },
    });

    // 关闭弹窗
    setPopupVisible(false);
  };

  // 权限获得后的回调
  const customWebPermissonConsent = () => {
    if (chooseImageRef.current === "camera") {
      chooseImage("camera");
    } else {
      chooseImage("album");
    }
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    permissionPopupProps,
    platformRef,
  } = usePermission(customWebPermissonConsent);
  const refreshFreight = async (deliveryId?: string) => {
    console.log(
      "refreshFreight",
      deliveryId ? `with deliveryId: ${deliveryId}` : ""
    );

    // 检查地址ID是否存在
    if (!address.id) {
      console.log("地址ID为空，跳过运费计算");
      return;
    }

    // 检查配送方式ID是否存在 - 优先使用传入的deliveryId
    const currentDeliveryId = deliveryId || singleValue[0];
    if (!currentDeliveryId || currentDeliveryId === "") {
      console.log("配送方式ID为空，跳过运费计算");
      return;
    }

    // 检查是否有选中的商品
    if (!selectedItems || selectedItems.length === 0) {
      console.log("没有选中的商品，跳过运费计算");
      return;
    }
    try {
      // 构建动态商品数量对象 - 格式: {"dynamicsId": quantity}
      const dynamicsQuantity: Record<string, number> = {};

      selectedItems.forEach((sellerCart) => {
        sellerCart.shoppingCartList.forEach((cartItem) => {
          const dynamicsId = String(cartItem.dynamicsId);
          // 计算该动态商品的总数量
          const totalQuantity = cartItem.details.reduce(
            (sum, detail) => sum + detail.quantity,
            0
          );

          // 如果该 dynamicsId 已存在,累加数量;否则初始化
          if (dynamicsQuantity[dynamicsId]) {
            dynamicsQuantity[dynamicsId] += totalQuantity;
          } else {
            dynamicsQuantity[dynamicsId] = totalQuantity;
          }
        });
      });

      const freightParams = {
        // receivingAddressId: address.id,
        // deliveryId: currentDeliveryId,
        // sellerUserId: selectedItems[0]?.shoppingCartSellerUserId || "",
        areaId: address.areaId,
        dynamicsQuantity: dynamicsQuantity,
      };

      console.log("运费计算参数:", freightParams);

      const res = await getFreight(freightParams);
      if (res && res.code === 0) {
        console.log("运费计算成功:", res.data);
        // 设置运费状态
        if (res.data && typeof res.data.freightAmount === "number") {
          setFreight(res.data.freightAmount);
        } else if (res.data && typeof res.data === "number") {
          setFreight(res.data);
        } else {
          console.log("运费数据格式异常:", res.data);
          setFreight(0);
        }
        setFreightCalculated(true); // 运费计算成功
      } else {
        console.log("运费计算失败:", res.msg);
        setFreight(0);
        setFreightCalculated(false); // 运费计算失败
        errMsg.current = res.msg;
        // 显示错误提示
        toast("error", {
          content: res.msg || "运费计算失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("运费计算出错:", error);
      setFreightCalculated(false); // 运费计算失败
      toast("error", {
        content: "运费计算失败，请重试",
        duration: 2000,
      });
    }
  };
  // 获取收货地址
  const fetchReceivingAddress = async () => {
    try {
      const userInfo = Taro.getStorageSync("userInfo");
      if (!userInfo || !userInfo.id) {
        console.log("用户信息获取失败");
        return;
      }

      const res = await getReceivingAddress({
        pageNo: 1,
        pageSize: 10,
      });

      console.log("获取收货地址响应:", res);

      if (res && res.code === 0 && res.data && res.data.length > 0) {
        const userAddresses = res.data.filter(
          (addr: any) => addr.defaultStatus === true
        );

        if (userAddresses.length > 0) {
          const addressData = userAddresses[0]; // 取筛选后的第一个默认地址
          console.log("addressData", addressData);
          setAddress({
            id: addressData.id,
            name: addressData.name,
            mobile: addressData.mobile,
            areaId: addressData.areaId,
            areaName: addressData.areaName,
            detailAddress: addressData.detailAddress,
          });
          console.log("地址信息已更新:", addressData);
        } else {
          console.log("当前用户暂无收货地址");
        }
      } else {
        console.log("获取收货地址失败:", res.msg);
      }
    } catch (error) {
      console.error("获取收货地址异常:", error);
    }
  };

  // 选择留言图片
  const handleChooseRemarkImage = () => {
    const count = 9 - uploadedImages.length;
    if (count <= 0) {
      toast("error", {
        content: "最多只能上传9张图片",
        duration: 2000,
      });
      return;
    }
    if (platformRef.current === "WX") {
      chooseImage("album");
      return;
    }

    // 显示选择图片来源的弹窗
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  // 处理用户选择（拍照/相册）
  const handleConfirm = (index: number) => {
    if (index === 0) {
      // 设置图片选择类型为拍照
      chooseImageRef.current = "camera";
      if (platformRef.current === "HM") {
        chooseImage("camera");
      } else {
        // 在 H5 环境下直接执行，在原生环境下检查权限
        if (!hasPermission(AuthTypes.CAMERA)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.CAMERA);
          return;
        }
      }
      chooseImage("camera");
    } else if (index === 1) {
      // 设置图片选择类型为相册
      chooseImageRef.current = "album";
      // 在 H5 环境下直接执行，在原生环境下检查权限
      if (platformRef.current === "HM") {
        chooseImage("album");
      } else {
        // 请求相册权限
        if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.GALLERY_PHOTO);
          return;
        }
        chooseImage("album");
      }
    }
  };

  // 删除留言图片
  const handleDeleteRemarkImage = (index: number) => {
    const newImages = [...uploadedImages];
    newImages.splice(index, 1);
    setUploadedImages(newImages);
  };

  // 获取配送方式列表
  const fetchDeliveryOptions = async (items: SellerCart[]) => {
    try {
      const userInfo = Taro.getStorageSync("userInfo");

      // 1. 先获取所有配送模板
      const templateRes = await getDeliveryTemplateList({
        pageNo: 1,
        pageSize: 100,
        userId: items[0]?.shoppingCartSellerUserId || "",
      });

      if (
        templateRes &&
        templateRes.code === 0 &&
        templateRes.data &&
        templateRes.data.list
      ) {
        console.log("[支付页面] 配送模板列表数据:", templateRes.data.list);
        const templateList = templateRes.data.list;

        // 2. 获取用户的配送配置
        const deliveryRes = await getDeliveryList({
          pageNo: 1,
          pageSize: 100,
          userId: items[0]?.shoppingCartSellerUserId || "",
        });

        console.log("[支付页面] 获取配送方式列表返回:", deliveryRes);

        let userConfigList: any[] = [];
        if (
          deliveryRes &&
          deliveryRes.code === 0 &&
          deliveryRes.data &&
          deliveryRes.data.list
        ) {
          userConfigList = deliveryRes.data.list;
        }

        // 3. 合并模板和用户配置
        const mergedList = templateList.map((template: any) => {
          // 查找 getDeliveryList 中 deliveryTemplateId 与当前模板 id 匹配的配置
          const userConfig = userConfigList.find(
            (config: any) => config.deliveryTemplateId === template.id
          );

          return {
            deliveryTemplateId: template.id,
            name: template.name,
            id: userConfig?.id,
            userId: userConfig?.userId,
            orderReminder: userConfig?.orderReminder || "",
            isEnable:
              userConfig?.isEnable !== undefined ? userConfig.isEnable : 0,
            createTime: userConfig?.createTime || template.createTime,
          };
        });

        console.log("[支付页面] 合并后的配送方式数据:", mergedList);

        // 4. 只获取启用的配送方式
        // const enabledDeliveries = mergedList.filter(
        //   (item: any) => item.isEnable === 1
        // );
        const options = mergedList.map((item: any) => ({
          label: item.name,
          value: item.deliveryTemplateId.toString(), // 使用模板ID作为value
          orderReminder: item.orderReminder, // 保存下单提示
        }));

        console.log("[支付页面] 启用的配送方式:", options);
        setDeliveryOptions(options);

        // 如果当前没有选中配送方式，自动选中第一项
        if (options.length > 0 && (!singleValue[0] || singleValue[0] === "")) {
          setSingleValue([options[0].value]);
        }
      }
    } catch (error) {
      console.error("获取配送方式失败:", error);
      // 如果获取失败，使用默认配送方式
      const defaultOptions = [
        { label: "快递", value: "1" },
        { label: "顺丰到付", value: "2" },
        { label: "自提", value: "6" },
        { label: "其他", value: "7" },
      ];
      setDeliveryOptions(defaultOptions);

      // 如果当前没有选中配送方式，自动选中第一项
      if (
        defaultOptions.length > 0 &&
        (!singleValue[0] || singleValue[0] === "")
      ) {
        setSingleValue([defaultOptions[0].value]);
      }
    }
  };

  // 数量加减
  const handleCountChange = (
    sellerIdx: number,
    itemIdx: number,
    detailIdx: number,
    value: number
  ) => {
    const clampedValue = Math.min(Math.max(value, 1), 9999);
    console.log("clampedValue", clampedValue);

    setSelectedItems((prev) => {
      const newItems = [...prev];
      newItems[sellerIdx].shoppingCartList[itemIdx].details[
        detailIdx
      ].quantity = clampedValue;
      return newItems;
    });

    refreshFreight();
  };

  // 处理下单
  const handleCreateOrder = async () => {
    if (!freightCalculated) {
      toast("error", {
        content: errMsg.current,
        duration: 2000,
      });
      return;
    }
    console.log("selectedItems", selectedItems);

    // 验证地址信息
    if (!address.name || !address.mobile || !address.detailAddress) {
      toast("error", {
        content: "请完善收货地址",
        duration: 2000,
      });
      return;
    }

    // 验证商品信息
    if (selectedItems.length === 0) {
      toast("error", {
        content: "请选择商品",
        duration: 2000,
      });
      return;
    }

    // 如果开启了自定义发件人，验证发件人信息
    if (customSender && (!senderName || !senderPhone)) {
      toast("error", {
        content: "请完善发件人信息",
        duration: 2000,
      });
      return;
    }

    // 验证配送方式
    if (!singleValue[0] || singleValue[0] === "") {
      toast("error", {
        content: "请选择配送方式",
        duration: 2000,
      });
      return;
    }

    // 验证自定义发件人手机号格式
    if (customSender && senderPhone) {
      const phoneReg = /^1\d{10}$/;
      if (!phoneReg.test(senderPhone)) {
        toast("error", {
          content: "请输入正确的手机号格式",
          duration: 2000,
        });
        return;
      }
    }

    setOrderLoading(true);

    try {
      // 构建备注信息
      const remarkText = globalRemark.trim();

      // 构建备注图片（多张图片用逗号分隔）
      const pictureRemark =
        uploadedImages.length > 0 ? uploadedImages.join(",") : undefined;

      // 计算总价
      let totalPrice = 0;
      selectedItems.forEach((seller) => {
        seller.shoppingCartList.forEach((item) => {
          const itemTotalQuantity = item.details.reduce((sum, detail) => sum + detail.quantity, 0);
          totalPrice += item.price * itemTotalQuantity;
        });
      });

      // 构建订单商品列表
      const orderDynamics = selectedItems.flatMap((seller) =>
        seller.shoppingCartList.map((item) => {
          const itemTotalQuantity = item.details.reduce((sum, detail) => sum + detail.quantity, 0);
          return {
            dynamicId: item.dynamicsId,
            count: itemTotalQuantity,
            price: item.price,
            orderDetails: item.details.map((detail) => {
              // 构建规格属性字符串
              const properties = JSON.stringify({
                colors: detail.productColorName || "",
                specifications: detail.productSpecificationsName || ""
              });

              return {
                cartDetailId: detail.id || 0,
                quantity: detail.quantity,
                properties: properties
              };
            })
          };
        })
      );

      // 构建新的订单数据结构
      const orderData = {
        userId: parseInt(Taro.getStorageSync("userInfo").id),
        sellerUserId: selectedItems[0]?.shoppingCartSellerUserId || "0", // 取第一个商家的ID
        totalPrice: totalPrice, // 转换为分
        deliveryPrice: freight, // 运费转换为分
        deliveryType: 1, // 配送类型，默认为1
        receiverName: (address as any).name || "",
        receiverMobile: (address as any).mobile || "",
        receiverAreaId: (address as any).areaId || 0,
        receiverDetailAddress: (address as any).detailAddress || "",
        isEnabledCustomizeSender: customSender ? 1 : 0, // 是否开启自定义发件人
        customizeSenderName: customSender ? senderName : undefined,
        customizeSenderPhone: customSender ? senderPhone : undefined,
        remark: remarkText || undefined,
        pictureRemark: pictureRemark,
        orderDynamics: orderDynamics,
        orderWay: platformRef.current === "WX" ? 2 : 1
      };

      console.log("订单数据:", orderData);

      const res = await createOrder(orderData);

      if (res && res.code === 0) {
        toast("success", {
          content: "下单成功",
          duration: 2000,
        });

        orderId.current = res.data;
        // 清除购物车选中的商品
        Taro.removeStorageSync("selectedItems");

           // 获取支付参数
      const payParamsResponse = await getOrderPayParams({ orderId: orderId.current.toString(), channelCode: platformRef.current === "WX" ? "wx_lite" : "wx_app" });

        if (payParamsResponse && payParamsResponse.code === 0) {
          const payParams = payParamsResponse.data;
          console.log("支付参数:", payParams);

          if (platformRef.current === "HM") {
            window.harmony.wxPay(JSON.stringify(payParams));
          } else if (platformRef.current === "IOS") {
            window.webkit.messageHandlers.wxPayWithStr.postMessage(
              JSON.stringify(payParams)
            );
          } else if (platformRef.current === "Android") {
            window.wxPay.wxPay(JSON.stringify(payParams));
          } else if (platformRef.current === "WX") {
            wx.miniProgram.redirectTo({
              url:
                "/pages/wxpay/index?payParams=" +
                encodeURIComponent(JSON.stringify(payParams)),
            });
          }
        }else{
          Taro.redirectTo({
            url: "/pageOrder/order/details/index?id=" + orderId.current,
          });
        }

       
        // 跳转到订单详情或支付页面
        // setTimeout(() => {
        //   Taro.navigateBack();
        // }, 1500);
      } else {
        toast("error", {
          content: res.msg || "下单失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("下单失败:", error);
      toast("error", {
        content: "下单失败",
        duration: 2000,
      });
    } finally {
      setOrderLoading(false);
    }
  };

  const totalCount = selectedItems.reduce(
    (sum, seller) =>
      sum +
      seller.shoppingCartList.reduce(
        (sellerSum, item) =>
          sellerSum +
          item.details.reduce(
            (itemSum, detail) => itemSum + detail.quantity,
            0
          ),
        0
      ),
    0
  );

  const totalPrice = selectedItems.reduce(
    (sum, seller) =>
      sum +
      seller.shoppingCartList.reduce(
        (sellerSum, item) =>
          sellerSum +
          item.details.reduce(
            (itemSum, detail) =>
              itemSum + ((item.price || 0) / 100) * detail.quantity,
            0
          ),
        0
      ),
    0
  );

  const payPrice = totalPrice + freight/100;

  // 配送方式选择器状态
  const [deliveryVisible, setDeliveryVisible] = useState(false);
  const [singleValue, setSingleValue] = useState([""]);

  const single = React.useMemo(() => {
    return [deliveryOptions.length > 0 ? deliveryOptions : []];
  }, [deliveryOptions]);

  //const address = Taro.getStorageSync('address');
  // useDidShow(() => {
  //   const savedAddress = Taro.getStorageSync('address') || {};
  //   console.log('从存储中获取的地址信息:', savedAddress);
  //   setAddress(savedAddress);
  // });
  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    // 使用全局回调管理器注册支付回调
    const callbackCleanup = useGlobalCallbacks("orderPay", {
      webPaySuc: webPaySuc,
      wxPayWithStr: webPaySuc,
      aliPayWithStr: webPaySuc,
    });

    const items = Taro.getStorageSync("selectedItems") || [];
    console.log('items11111111111', items);
    setSelectedItems(items);

    // 初始化留言编辑状态
    // 初始化全局留言（如果有的话）
    const existingRemarks = items.flatMap((seller) =>
      seller.shoppingCartList
        .filter((item) => item.remark && item.remark.trim() !== "")
        .map((item) => item.remark)
    );
    if (existingRemarks.length > 0) {
      setGlobalRemark(existingRemarks.join("\n"));
    }

    // 初始化运费计算状态为 false
    setFreightCalculated(false);

    // 获取最新的收货地址
    fetchReceivingAddress();

    // 获取配送方式列表
    fetchDeliveryOptions(items);

    // 判断入口来源
    const router = Taro.getCurrentInstance().router;
    const fromPage = router?.params?.from || "";

    // 如果是从动态详情页来的，设置为非购物车下单
    if (fromPage === "detail") {
      setIsFromCart(false);
    } else {
      // 默认认为是从购物车来的
      setIsFromCart(true);
    }

    // 监听地址更新事件
    const handleAddressUpdated = (newAddress: any) => {
      console.log("收到地址更新事件:", newAddress);
      setAddress({
        id: newAddress.id,
        name: newAddress.name,
        mobile: newAddress.mobile,
        areaId: newAddress.areaId,
        areaName: newAddress.areaName,
        detailAddress: newAddress.detailAddress,
      });
      refreshFreight();
    };

    Taro.eventCenter.on("addressUpdated", handleAddressUpdated);

    console.log(
      "入口来源:",
      fromPage,
      "是否购物车下单:",
      fromPage !== "detail"
    );
    console.log(items, "-------------------");

    // 清理事件监听
    return () => {
      Taro.eventCenter.off("addressUpdated", handleAddressUpdated);
      callbackCleanup && callbackCleanup();
      cleanup && cleanup();
    };
  }, []);

  // 监听地址和配送方式变化，自动计算运费
  useEffect(() => {
    if (address.id && singleValue[0] && selectedItems.length > 0) {
      setFreightCalculated(false); // 重置状态
      refreshFreight();
    }
  }, [address.id, singleValue[0], selectedItems.length]);

  const webPaySuc = (status: number) => {
    console.log("webPaySuc", status);
    if (isFromCart) {
      Taro.eventCenter.trigger("refreshCartList");
    }
    if (status === 1) {
      toast("success", {
        content: "支付成功",
        duration: 2000,
      });
      //如果是购物车下单，则刷新购物车
      // if (isFromCart) {
      //   Taro.eventCenter.trigger('refreshCartList');
      // }else{
      //   Taro.navigateBack();
      // }
    } else if (status === 2) {
      toast("error", {
        content: "支付失败",
        duration: 2000,
      });
    } else if (status === 3) {
      toast("error", {
        content: "支付取消",
        duration: 2000,
      });
    }

    //关闭当前页，跳转到订单详情页
    Taro.redirectTo({
      url: "/pageOrder/order/details/index?id=" + orderId.current,
    });
  };

  return (
    <View className="pay-order-page">
      {platformRef.current !== "WX" && <YkNavBar title="确认订单" />}
      <View className="pay-list-section">
        <Cell.Group bordered={false}>
          <Cell
            label={
              address.detailAddress ? (
                <div className="demo-cell-avatar-label">
                  <span>{address.detailAddress}</span>
                </div>
              ) : (
                <div className="demo-cell-avatar-label">
                  <Avatar
                    src={require("@/assets/images/common/add_address.png")}
                  />
                  <span>添加收货地址</span>
                </div>
              )
            }
            {...(address?.name &&
            address?.mobile &&
            address?.areaName &&
            address?.detailAddress
              ? {
                  prepend: (
                    <div
                      style={{
                        fontSize: 12,
                        lineHeight: "18px",
                        marginBottom: -15,
                        paddingTop: 16,
                      }}
                    >
                      {address.areaName}
                    </div>
                  ),
                  append: (
                    <div
                      style={{
                        fontSize: 12,
                        lineHeight: "18px",
                        marginTop: -15,
                        paddingBottom: 16,
                      }}
                    >
                      {address.name} {address.mobile}
                    </div>
                  ),
                }
              : {})}
            showArrow
            onClick={() => {
              // 将当前地址信息作为参数传递
              const addressParams = {
                id: address.id || "",
                name: address.name || "",
                mobile: address.mobile || "",
                areaId: address.areaId || "",
                areaName: address.areaName || "",
                detailAddress: address.detailAddress || "",
              };
              const queryString = Object.keys(addressParams)
                .map(
                  (key) => `${key}=${encodeURIComponent(addressParams[key])}`
                )
                .join("&");

              Taro.navigateTo({
                url: `/pageOrder/order/pay/address?${queryString}`,
              });
            }}
          />

          <Cell
            label={<Text>配送方式</Text>}
            className="pay-list-cell"
            text={(() => {
              const selectedOption = deliveryOptions.find(
                (option) => option.value === singleValue[0]
              );
              return selectedOption ? selectedOption.label : "请选择配送方式";
            })()}
            showArrow
            onClick={() => {
              if (deliveryOptions.length > 0) {
                setDeliveryVisible(true);
              }else{
                toast("error", {
                  content: "没有启用的配送方式，请先设置",
                  duration: 2000,
                });
              }
            }}
          />

          {/* 下单提示显示区域 */}
          {(() => {
            const selectedOption = deliveryOptions.find(
              (option) => option.value === singleValue[0]
            );
            return selectedOption && selectedOption.orderReminder ? (
              <View className="order-reminder-section">
                <Text className="order-reminder-text">
                  {selectedOption.orderReminder}
                </Text>
              </View>
            ) : null;
          })()}

          <Cell label={<Text>自定义发件人</Text>} className="pay-list-cell">
            <Switch
              platform="android"
              checked={customSender}
              onChange={setCustomSender}
            />
          </Cell>
          {customSender && (
            <View>
              <View className="custom-sender-panel">
                <View className="custom-sender-row">
                  <Text className="custom-sender-label">发件人</Text>
                  <Input
                    className="custom-sender-input"
                    placeholder="默认为商家，可修改"
                    value={senderName}
                    disabled={!customSender}
                    onInput={(e) => setSenderName(e.detail.value)}
                  />
                </View>
                <View className="custom-sender-row">
                  <Text className="custom-sender-label">手机号</Text>
                  <Input
                    className="custom-sender-input"
                    type="number"
                    placeholder="默认为商家手机号，可修改"
                    value={senderPhone}
                    disabled={!customSender}
                    maxlength={11}
                    onInput={(e) => {
                      const value = e.detail.value;
                      // 只允许输入数字，并限制为11位
                      const phoneValue = value.replace(/\D/g, "").slice(0, 11);
                      setSenderPhone(phoneValue);
                    }}
                  />
                </View>
              </View>
              <View className="empty-row" style={{ height: "12px" }}></View>
            </View>
          )}
        </Cell.Group>
      </View>
      <View className="pay-goods-section">
        {selectedItems.map((seller, sellerIdx) => (
          <View key={seller.shoppingCartSellerId}>
            <View className="shop-title-row">
              <Avatar src={seller.dynamicsUserAvatar} />
              <Text className="shop-title">{seller.dynamicsUserName}</Text>
              <Text className="shop-cert" />
            </View>

            {seller.shoppingCartList.map((item, itemIdx) => (
              <View className="goods-row-pay" key={item.id}>
                <View className="goods-info-pay">
                  {item.dynamicsImage?.split(",")[0] ? (
                    <Image
                      className="goods-img"
                      src={item.dynamicsImage.split(",")[0]}
                    />
                  ) : (
                    <View className="goods-img goods-img-placeholder" />
                  )}
                  <Text className="goods-title">{item.dynamicsContent}</Text>
                  <View
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                      alignItems: "flex-end",
                      flexDirection: "column",
                    }}
                  >
                    <Text className="goods-price">
                      ￥{((item.price || 0) / 100).toFixed(2)}
                    </Text>
                    <Text className="goods-count">
                      x
                      {item.details.reduce(
                        (sum, detail) => sum + detail.quantity,
                        0
                      )}
                    </Text>
                  </View>
                </View>
                {item.details.map((detail, detailIdx) => (
                  <View className="goods-bottom-row" key={detail.id}>
                    <Text className="goods-size">
                      {detail.productColorName &&
                      detail.productSpecificationsName
                        ? `${detail.productColorName} | ${detail.productSpecificationsName}`
                        : detail.productColorName ||
                          detail.productSpecificationsName ||
                          "默认"}
                    </Text>
                    <Stepper
                      value={detail.quantity}
                      defaultValue={detail.quantity}
                      min={1}
                      max={9999}
                      onChange={(v) =>
                        handleCountChange(sellerIdx, itemIdx, detailIdx, v || 1)
                      }
                    />
                  </View>
                ))}
              </View>
            ))}
          </View>
        ))}

        <View className="pay-summary-list">
          <View className="pay-summary-row">
            <Text>商品数量</Text>
            <Text>{totalCount}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>商品总金额</Text>
            <Text>¥{totalPrice.toFixed(2)}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>运费</Text>
            <Text>¥{(freight/100).toFixed(2)}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>实付金额</Text>
            <Text className="pay-price">¥{payPrice.toFixed(2)}</Text>
          </View>
        </View>

        {/* 留言区域 */}
        <View className="order-remark-section">
          <View className="order-remark-header">
            <View className="order-remark-content">
              {/* 统一的留言输入框 */}
              <Input
                className="order-remark-input"
                placeholder="请输入留言信息"
                value={globalRemark}
                onInput={(e) => setGlobalRemark(e.detail.value)}
              />
            </View>
            <View
              className="order-remark-camera"
              onClick={handleChooseRemarkImage}
            >
              <Image
                className="camera-icon"
                src={require("@/assets/images/common/add_picture.png")}
              />
            </View>
          </View>

          {/* 图片上传区域 */}
          {uploadedImages.length > 0 && (
            <View className="uploaded-images">
              {uploadedImages.map((image, index) => (
                <View key={index} className="uploaded-image-item">
                  <Image src={image} className="uploaded-image" />
                  <View
                    className="delete-image-btn"
                    onClick={() => handleDeleteRemarkImage(index)}
                  >
                    ×
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
      <View className="pay-bottom-bar">
        <View className="pay-bottom-info">
          <Text className="pay-bottom-count">共{totalCount}件</Text>
          <Text className="pay-bottom-price">¥{payPrice.toFixed(2)}</Text>
        </View>
        <Button
          type="primary"
          className="pay-bottom-btn"
          loading={orderLoading}
          disabled={orderLoading}
          onClick={handleCreateOrder}
        >
          {orderLoading ? "下单中..." : `立即支付 ¥${payPrice.toFixed(2)}`}
        </Button>
      </View>

      {/* 配送方式选择器 */}
      <Picker
        visible={deliveryVisible}
        cascade={false}
        data={single}
        maskClosable
        onHide={() => {
          setDeliveryVisible(false);
        }}
        onOk={(val) => {
          console.log("------配送方式选择", val);
          setSingleValue(val as string[]);
          setDeliveryVisible(false);
          // 使用选中的值直接调用运费计算
          refreshFreight((val as string[])[0]);
        }}
        value={singleValue}
      />

      {/* 底部弹出对话框 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
}
