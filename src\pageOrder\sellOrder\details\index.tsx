import { View, Text, Input } from "@tarojs/components";
import { useLoad, useDidShow } from "@tarojs/taro";
import wx from "weixin-webview-jssdk";
import "./index.less";
import {
  NavBar,
  Image,
  Avatar,
  Divider,
  Tag,
  Loading,
  Dialog,
  Popup,
  Button,
} from "@arco-design/mobile-react";
import { IconRight,IconCopy,IconPhone,IconDownload,IconDown,IconClose } from "@arco-iconbox/react-yk-arco";
import { IconPayment } from "@/components/YkIcons";
import React, { useState, useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import areaDataType from '@/constants/area.json'; // 引入公有目录下的完整地区数据

import {
  getUserOrderDetails,
  getOrderPayParams,
  cancelOrder,
  uploadFile,
  updateOrderRemark,
  getDeliveryOrder,
  updateDeliveryOrder,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { ids } from "webpack";
import { toast } from "@/utils/yk-common";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";
import { AuthTypes } from "@/utils/config/authTypes";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
export default function OrderDetails() {
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [logisticsData, setLogisticsData] = useState<any[]>([]);
  const [activeBtn, setActiveBtn] = useState(-1); // -1表示无高亮按钮
  const [isExpanded, setIsExpanded] = useState(false); // 控制时间信息展开/收起
  const [showTrackingModal, setShowTrackingModal] = useState(false); // 控制修改快递单号弹框
  const [currentLogisticsIndex, setCurrentLogisticsIndex] = useState(0); // 当前编辑的物流索引
  const [trackingNumber, setTrackingNumber] = useState(""); // 快递单号
  const [deliveryCompany, setDeliveryCompany] = useState(""); // 快递公司
  // 上传的图片列表
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  // 发货弹窗状态
  const [showDeliveryPopup, setShowDeliveryPopup] = useState(false);

  // 图片选择弹窗状态
  const [isPopupVisible, setPopupVisible] = useState(false);
  const chooseImageRef = useRef<"album" | "camera">("album");

    // 根据地区 ID 查找对应的名称
const findAreaNameById = (areaId: string): string => {
  for (const province of areaDataType) {
    if (province.id === areaId) {
      return province.name;
    }
    for (const city of province.children || []) {
      if (city.id === areaId) {
        return city.name;
      }

      for (const area of city.children || []) {
        if (area.id === areaId) {
          return `${province.name} ${city.name} ${area.name}`;
        }
      }
    }
  }
  return '';
};

    // 保存我的备注到服务器
    const saveSellerRemark = async (customImages?: string[]) => {
      const imagesToSave = customImages || uploadedImages;
      console.log("uploadedImages", imagesToSave);
      if (!orderDetails?.id) return;
  
      try {
        const response = await updateOrderRemark({
          id: orderDetails.id,
          sellerRemark: orderDetails.sellerRemark || "",
          sellerPictureRemark: imagesToSave.join(",") || "",
        });
  
        if (response && (response as any).code === 0) {
          // toast("success", {
          //   content: "备注保存成功",
          //   duration: 2000,
          // });
        } else {
          // toast("error", {
          //   content: "备注保存失败",
          //   duration: 2000,
          // });
        }
      } catch (error) {
        console.error("保存备注失败:", error);
        toast("error", {
          content: "备注保存失败",
          duration: 2000,
        });
      }
    };

    // 选择图片的方法
    const chooseImage = (sourceType: "album" | "camera") => {
      const count = 9 - uploadedImages.length;
  if(platformRef.current === "Android"){
    window.setPhotoNum?.setPhotoNum(count);
  }
  if(platformRef.current === "HM"){
    (window as any).harmony.setPhotoNum(count);
  }
      Taro.chooseImage({
        count,
        sizeType: ["original", "compressed"],
        sourceType: [sourceType],
        success: async (res) => {
          console.log("选择的图片:", res.tempFilePaths);
  
          // 显示上传进度
          Taro.showLoading({
            title: "上传中...",
          });
  
          try {
            // 上传所有选择的图片
            const uploadPromises = res.tempFilePaths.map(async (imagePath) => {
              const uploadRes = await uploadFile([imagePath]);
              console.log("上传结果:", uploadRes);
  
              // 根据实际返回结构提取URL
              let url = "";
              if (uploadRes && uploadRes.code === 0) {
                if (typeof uploadRes.data === "string") {
                  url = uploadRes.data;
                } else if (uploadRes.data && uploadRes.data.url) {
                  url = uploadRes.data.url;
                } else if (Array.isArray(uploadRes.data) && uploadRes.data[0]) {
                  url = uploadRes.data[0];
                }
              }
  
              if (!url) {
                throw new Error("上传失败，未获取到图片URL");
              }
  
              return url;
            });
  
            const uploadedUrls = await Promise.all(uploadPromises);
            console.log("所有图片上传完成:", uploadedUrls);
  
            // 更新上传的图片列表
            setUploadedImages((prev) => {
              const newImages = [...prev, ...uploadedUrls];
              // 图片上传完成后自动保存备注，传递新的图片数组
              setTimeout(() => {
                saveSellerRemark(newImages);
              }, 100);
              return newImages;
            });
  
            Taro.hideLoading();
            // toast("success", {
            //   content: "上传成功",
            //   duration: 2000,
            // });
          } catch (error) {
            console.error("上传图片失败:", error);
            Taro.hideLoading();
            // toast("error", {
            //   content: "上传失败，请重试",
            //   duration: 2000,
            // });
          }
        },
        fail: (error) => {
          console.error("选择图片失败:", error);
          // toast("error", {
          //   content: "选择图片失败",
          //   duration: 2000,
          // });
        },
      });
  
      // 关闭弹窗
      setPopupVisible(false);
    };
    
  // 权限获得后的回调
  const customWebPermissonConsent = () => {
    if (chooseImageRef.current === "camera") {
      chooseImage("camera");
    } else {
      chooseImage("album");
    }
  };

  
  // 使用全局权限管理
  const { initPermissions, hasPermission, requestPermission, permissionPopupProps, platformRef } =
    usePermission(customWebPermissonConsent);
  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    //刷新订单事件
    Taro.eventCenter.on("refreshOrderList", fetchOrderDetails);
    //刷新物流数据事件
    Taro.eventCenter.on("refreshLogisticsData", fetchDeliveryOrder);
    // 使用全局回调管理器注册支付回调
    const callbackCleanup = useGlobalCallbacks('sellOrderDetails', {
      webPaySuc: webPaySuc,
      wxPayWithStr: webPaySuc,
      aliPayWithStr: webPaySuc,
    });

    // 清理事件监听
    return () => {
      Taro.eventCenter.off("refreshOrderList", fetchOrderDetails);
      Taro.eventCenter.off("refreshLogisticsData", fetchDeliveryOrder);
      callbackCleanup && callbackCleanup();
      cleanup && cleanup();
    };
  }, []);

  useDidShow(() => {
    const selectedCompany = Taro.getStorageSync("selectedDeliveryCompany");
    if (selectedCompany && selectedCompany.name) {
      setDeliveryCompany(selectedCompany.name);
      // 重新打开弹框
      setShowTrackingModal(true);
      // 清除缓存
      Taro.removeStorageSync("selectedDeliveryCompany");
    }
  });

  const webPaySuc = (status: number) => {
    console.log("webPaySuc", status);
    if (status === 1) {
      toast("success", {
        content: "支付成功",
        duration: 2000,
      });
      //刷新订单详情
      fetchOrderDetails();
    } else if (status === 2) {
      toast("error", {
        content: "支付失败",
        duration: 2000,
      });
    } else if (status === 3) {
      toast("error", {
        content: "支付取消",
        duration: 2000,
      });
    }
  };

  const isFirstShowRef = useRef(true);
  useDidShow(() => {
    console.log("页面显示，检查是否需要刷新订单");
    // 第一次显示是页面初始化，不需要刷新（因为useEffect中已经加载了数据）
    if (isFirstShowRef.current) {
      isFirstShowRef.current = false;
      return;
    }
    
    // 微信小程序环境下，从其他页面返回时刷新订单列表
    // 这样可以确保从支付页面返回后能及时更新订单状态
    if (platformRef.current === "WX") {
      console.log("微信小程序环境：页面显示时刷新订单列表");
      fetchOrderDetails();
    }
  });

  // 处理支付逻辑
  const handlePayment = async () => {
    try {
      const orderId = getOrderId();
      if (!orderId) {
        toast("error", {
          content: "订单ID不存在",
          duration: 2000,
        });
        return;
      }

      // 获取支付参数
      const payParamsResponse = await getOrderPayParams({ orderId: orderId, channelCode: platformRef.current === "WX" ? "wx_lite" : "wx_app" });

      if (payParamsResponse && payParamsResponse.code === 0) {
        const payParams = payParamsResponse.data;
        console.log("支付参数:", payParams);

        if(platformRef.current === "HM"){
          window.harmony.wxPay(JSON.stringify(payParams));
        }else if(platformRef.current === "IOS"){
          window.webkit.messageHandlers.wxPayWithStr.postMessage(JSON.stringify(payParams));
        }else if(platformRef.current === "Android"){
          window.wxPay.wxPay(JSON.stringify(payParams));
        } else if (platformRef.current === "WX") {
          payParams.userOrderId = orderId
          wx.miniProgram.navigateTo({
            url: '/pages/wxpay/index?payParams=' + encodeURIComponent(JSON.stringify(payParams))
          });
        } 
      } else {
        toast("error", {
          content: payParamsResponse.msg || "获取支付信息失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("支付处理失败:", error);
      toast("error", {
        content: "支付处理失败",
        duration: 2000,
      });
    }
  };

  // 处理取消订单（参考订单列表实现）
  const handleCancelOrder = async () => {
    try {
      const orderId = getOrderId();
      if (!orderId) {
        toast("error", {
          content: "订单ID不存在",
          duration: 2000,
        });
        return;
      }

      Dialog.confirm({
        title: "确认取消？",
        children: "取消后订单将会失效且无法恢复,请谨慎操作!",
        okText: "确定",
        cancelText: "取消",
        platform: "ios",
        onOk: async () => {
          // 显示加载提示
          Taro.showLoading({
            title: "正在取消订单...",
          });

          // 调用取消订单接口
          const cancelResult = await cancelOrder({ id: orderId });

          Taro.hideLoading();

          if (cancelResult && cancelResult.code === 0) {
            // 取消成功，显示成功提示
            toast("success", {
              content: "订单已取消",
              duration: 2000,
            });

            // 刷新订单详情
            fetchOrderDetails();
          } else {
            // 取消失败，显示错误信息
            toast("error", {
              content: cancelResult.msg || "取消订单失败，请重试",
              duration: 2000,
            });
          }
        },
      });
    } catch (error) {
      Taro.hideLoading();
      console.error("取消订单失败:", error);
      toast("error", {
        content: "取消订单失败，请检查网络连接",
        duration: 2000,
      });
    }
  };

  // 获取路由参数中的订单ID
  const getOrderId = () => {
    const router = Taro.getCurrentInstance().router;
    return router?.params?.id || router?.params?.orderId;
  };

  const fetchDeliveryOrder = async () => {
    const orderId = getOrderId();
    if (!orderId) {
      toast("info", {
        content: "订单ID不存在",
        duration: 2000
      });
      return;
    }

    try {
      const response = await getDeliveryOrder({ orderId: orderId });

      if (response && response.code === 0 && response.data && response.data.list) {
        setLogisticsData(response.data.list);
      } else {
        toast("error", {
          content: response.msg || "获取物流信息失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取物流信息失败:", error);
      toast("info", {
        content: "获取物流信息失败",
        duration: 2000
      });
    }
  };

  // 获取订单详情
  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const orderId = getOrderId();

      if (!orderId) {
        toast("info", {
        content: "订单ID不存在",
        duration: 2000
      });
        return;
      }

      const response = await getUserOrderDetails({ id: orderId });

      if (response && response.code === 0) {
        setOrderDetails(response.data);

        // 初始化已上传的图片列表
        if (response.data.sellerPictureRemark) {
          const existingImages = response.data.sellerPictureRemark
            .split(",")
            .filter((img) => img.trim() !== ""); // 过滤空字符串
          setUploadedImages(existingImages);
        }
      } else {
        toast("error", {
          content: response.msg || "获取订单详情失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
      toast("info", {
        content: "获取订单详情失败",
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    fetchOrderDetails();
    fetchDeliveryOrder();
  });

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <View className="order-details-box">
       {platformRef.current !== "WX" && <YkNavBar title="订单详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Loading />
          <Text style={{ marginTop: "10px", display: "block" }}>加载中...</Text>
        </View>
      </View>
    );
  }

  // 如果没有订单数据，显示错误状态
  if (!orderDetails) {
    return (
      <View className="order-details-box">
        {platformRef.current !== "WX" &&<YkNavBar title="订单详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Text>订单详情不存在</Text>
        </View>
      </View>
    );
  }
  // 格式化时间
  const formatTime = (timestamp: number) => {
    if (!timestamp) return "--";
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 获取订单状态文本
  const getOrderStatusText = (orderType: number) => {
    switch (orderType) {
      case 1:
        return "待付款";
      case 2:
        return "待发货";
      case 3:
        if (orderDetails.shippingStatus === 1) {
          return "全部已发货";
        } else {
          return "部分未发货";
        }
      case 4:
        return "已完成";
      case 5:
        return "退款中";
      case 6:
        return "已退款";
      case 7:
        return "已取消";
      default:
        return "未知状态";
    }
  };

  // 打开修改快递单号弹框
  const handleEditTrackingNumber = (logisticsIndex: number) => {
    const logistics = getLogisticsData()[logisticsIndex];
    setCurrentLogisticsIndex(logisticsIndex);
    setTrackingNumber(logistics.trackingNumber);
    setDeliveryCompany(logistics.deliveryCompany);
    setShowTrackingModal(true);
  };

  // 选择快递公司
  const handleSelectDeliveryCompany = () => {
    // 关闭弹框
    setShowTrackingModal(false);
    // 跳转到快递公司选择页面
    Taro.navigateTo({
      url: "/pageOrder/deliveryCompany/index",
    });
  };

  // 保存快递单号修改
  const handleSaveTrackingNumber = async () => {
    if (!trackingNumber.trim()) {
      toast("error", {
        content: "请输入快递单号",
        duration: 2000,
      });
      return;
    }
    if (!deliveryCompany.trim()) {
      toast("error", {
        content: "请选择快递公司",
        duration: 2000,
      });
      return;
    }

    try {
      Taro.showLoading({ title: "保存中..." });

      // 获取当前物流记录
      const currentLogistics = getLogisticsData()[currentLogisticsIndex];
      if (!currentLogistics) {
        toast("error", {
          content: "物流信息不存在",
          duration: 2000,
        });
        return;
      }

      // 调用更新接口
      const response = await updateDeliveryOrder({
        id: currentLogistics.id,
        userOrderId: orderDetails.id,
        trackingNumber: trackingNumber,
        expressCompany: deliveryCompany,
      });

      if (response && response.code === 0) {
        toast("success", {
          content: "修改成功",
          duration: 2000,
        });

        // 刷新物流数据
        await fetchDeliveryOrder();

        setShowTrackingModal(false);
      } else {
        toast("error", {
          content: response.msg || "修改失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("修改快递单号失败:", error);
      toast("error", {
        content: "修改失败，请检查网络连接",
        duration: 2000,
      });
    } finally {
      Taro.hideLoading();
    }
  };

  // 复制订单号
  const handleCopyOrderNo = (orderNo: string) => {
    Taro.setClipboardData({
      data: orderNo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "订单号已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 切换展开/收起状态
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // 选择留言图片
  const handleChooseRemarkImage = () => {
    const count = 9 - uploadedImages.length;
    if (count <= 0) {
      toast("error", {
        content: "最多只能上传9张图片",
        duration: 2000,
      });
      return;
    }

    if (platformRef.current === "WX") {
      chooseImage("album");
      return;
    }

    // 显示选择图片来源的弹窗
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  // 处理用户选择（拍照/相册）
  const handleConfirm = (index: number) => {
    if (index === 0) {
      // 设置图片选择类型为拍照
      chooseImageRef.current = "camera";
      // 在 H5 环境下直接执行，在原生环境下检查权限
      if (platformRef.current === "HM") {
        chooseImage("camera");
      } else {
        // 请求相机权限
        if (!hasPermission(AuthTypes.CAMERA)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.CAMERA);
          return;
        }
        chooseImage("camera");
      }
    } else if (index === 1) {
      // 设置图片选择类型为相册
      chooseImageRef.current = "album";
      // 在 H5 环境下直接执行，在原生环境下检查权限
      if (platformRef.current === "HM") {
        chooseImage("album");
      } else {
        // 请求相册权限
        if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.GALLERY_PHOTO);
          return;
        }
        chooseImage("album");
      }
    }
  };



  // 删除留言图片
  const handleDeleteRemarkImage = (index: number) => {
    const newImages = [...uploadedImages];
    newImages.splice(index, 1);
    setUploadedImages(newImages);

    // 删除图片后自动保存备注，传递新的图片数组
    setTimeout(() => {
      saveSellerRemark(newImages);
    }, 100);
  };

  // 处理留言编辑
  const handleRemarkChange = (value: string) => {
    setOrderDetails({ ...orderDetails, sellerRemark: value });
  };



  // 复制发件人信息
  const handleCopySender = (senderInfo: string) => {
    Taro.setClipboardData({
      data: senderInfo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "发件人信息已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 复制快递单号
  const handleCopyTrackingNumber = (orderNo: string) => {
    Taro.setClipboardData({
      data: orderNo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "快递单号已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 复制收件人信息
  const handleCopyReceiver = (receiverInfo: string) => {
    Taro.setClipboardData({
      data: receiverInfo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "收件人信息已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };

  // 处理发货按钮点击
  const handleDeliveryClick = () => {
    setShowDeliveryPopup(true);
  };

  // 处理退款按钮点击
  const handleRefundClick = () => {
    Taro.navigateTo({
      url: `/pageOrder/refund/index?orderId=${orderDetails.id}`,
    });
  };

  // 处理退款详情按钮点击
  const handleRefundDetailClick = () => {
    Taro.navigateTo({
      url: `/pageOrder/refundList/index?orderId=${orderDetails.id}&orderType=seller`,
    });
  };

  // 处理发货类型选择
  const handleDeliveryTypeSelect = (type: "partial" | "full") => {
    setShowDeliveryPopup(false);

    // 跳转到发货页面
    Taro.navigateTo({
      url: `/pageOrder/delivery/index?orderId=${orderDetails.id}&type=${
        type === "partial" ? "1" : "2"
      }`,
    });
  };

  // 获取物流数据（使用真实接口数据）
  const getLogisticsData = () => {
    if (!logisticsData || logisticsData.length === 0) {
      return [];
    }

    console.log('logisticsData11111111111', logisticsData);

    return logisticsData.map((logistics: any) => {
      // 获取物流状态
      const status = logistics.trackResult
        ? logistics.trackResult.context
        : "查无物流信息";

      // 获取更新时间
      const updateTime = logistics.trackResult
        ? logistics.trackResult.ftime
        : new Date(logistics.createTime).toLocaleString();

      // 通过 shippingDetails 中的 dynamicId 获取商品信息
      const goods: any[] = [];
      let totalQuantity = 0;
      if (logistics.dynamic && orderDetails?.orderDynamics) {
        logistics.dynamic.forEach((shippingDetail: any) => {
          // 查找对应的 dynamics（注意：这里应该用 dynamicId 匹配 dynamics.dynamicsId）
          const dynamics = orderDetails.orderDynamics.find(
            (d: any) => d.dynamicId === shippingDetail.dynamicId
          );
          console.log('dynamics11111111111', dynamics);
          console.log('shippingDetail11111111111', shippingDetail);
          // if (dynamics) {
          //   // 查找对应的 orderDetail 来获取规格信息
          //   const orderDetail = dynamics.orderDetails?.find(
          //     (od: any) => od.id === shippingDetail.orderDetailId
          //   );

          //   console.log('orderDetail11111111111', orderDetail);
          //   goods.push({
          //     image: dynamics.dynamicPictures || "",
          //     title: dynamics.dynamicName || "商品名称",
          //     price: dynamics.price || "0",
          //     // spec: orderDetail
          //     //   ? `${orderDetail.colorName || ""} ${
          //     //       orderDetail.specName || ""
          //     //     }`.trim()
          //     //   : "",
          //     spec: orderDetail
          //       ? orderDetail.properties
          //       : "",
          //     quantity: shippingDetail.quantity || 0,
          //   });
          //   console.log('goods11111111111', goods);
          //   totalQuantity += shippingDetail.quantity || 0;
          // }

          if (dynamics) {
            // 遍历发货详情数组
            shippingDetail.shipmentDetails?.forEach((shipmentDetail: any) => {
              // 查找对应的 orderDetail 来获取规格信息
              const orderDetail = dynamics.orderDetails?.find(
                (od: any) => od.id === shipmentDetail.orderDetailId
              );
          
              console.log('orderDetail11111111111', orderDetail);
              
              let spec = "";
              if (orderDetail && orderDetail.properties) {
                try {
                  // 解析 properties JSON 字符串
                  const properties = JSON.parse(orderDetail.properties);
                  // 格式化显示颜色和规格
                  spec = `${properties.colors || ""} ${properties.specifications || ""}`.trim();
                } catch (error) {
                  console.error('解析 properties 失败:', error);
                  spec = orderDetail.properties; // 如果解析失败，直接显示原始字符串
                }
              }
          
              goods.push({
                image: dynamics.dynamicPictures || "",
                title: dynamics.dynamicName || "商品名称",
                price: dynamics.price || "0",
                spec: spec,
                quantity: orderDetail.quantity || 0,
                quantityShipped: shipmentDetail.quantity || 0,
              });
              
              totalQuantity += shipmentDetail.quantity || 0;
            });
            
            console.log('goods11111111111', goods);
          }
        });
      }

      return {
        id: logistics.id,
        status: status,
        updateTime: updateTime,
        trackingNumber: logistics.trackingNumber,
        deliveryCompany: logistics.expressCompany,
        goods: goods,
        totalQuantity: totalQuantity,
      };
    });
  };

  return (
    <View className="order-details-box">
      {platformRef.current !== "WX" &&<YkNavBar title="订单详情" />}
      <View className="order-status-bar-sell">
        {getOrderStatusText(orderDetails.status)}
      </View>

      <View className="order-user-card-sell">
        {/* 用户信息 */}
        <View className="user-info-header" onClick={(e) =>{
          e.stopPropagation();
          if(orderDetails.userId === Taro.getStorageSync("userInfo").userId){
            Taro.navigateTo({
              url: `/pageDynamic/album/index`,
            })
          }else{
            Taro.navigateTo({
              url: `/pageUserInfo/userDetail/index?userId=${orderDetails.userId}`,
            })
          }
        }}>
          <View className="user-info-content">
            <Avatar
              size="small"
              style={{ marginRight: 8 }}
              src={orderDetails.userAvatar || ""}
            />
            <Text className="user-name">{orderDetails.userName || ""}</Text>
          </View>
          {/* <Image
            className="arrow-right"
            src={require("@/assets/images/common/arrow_right.png")}
          /> */}
          <IconRight
            className="arrow-right"
          />
        </View>

        {/* 发件人信息 */}
        <View className="contact-info-row">
          <View className="contact-icon-wrapper">
            <Image
            bottomOverlap={null}
              className="contact-icon"
              src={require("@/assets/images/common/send_icon.png")}
            />
          </View>
          <View className="contact-info">
            <View className="contact-details">
              <View className="contact-details-receive">
                <View className="contact-name-row">
                  <Text className="contact-name">
                    {orderDetails.customizeSenderName ||
                      orderDetails.userName ||
                      ""}
                  </Text>
                  {/* <Image
                    className="copy-icon"
                    src={require("@/assets/images/common/copy_icon.png")}
                    onClick={() =>
                      handleCopySender(
                        `${
                          orderDetails.customizeSenderName ||
                          orderDetails.dynamicUserName ||
                          ""
                        } ${
                          orderDetails.customizeSenderPhone ||
                          orderDetails.dynamicUserPhone ||
                          ""
                        }`
                      )
                    }
                  /> */}
                  <IconCopy
                    className="copy-icon"
                    onClick={() =>
                      handleCopySender(
                        `${
                          orderDetails.customizeSenderName ||
                          orderDetails.userName ||
                          ""
                        } ${
                          orderDetails.customizeSenderPhone ||
                          orderDetails.userPhone ||
                          ""
                        }`
                      )
                    }
                  />
                </View>
                <Text className="contact-name">
                  {orderDetails.customizeSenderPhone ||
                    orderDetails.userPhone ||
                    ""}
                </Text>
              </View>
            </View>
            <View className="contact-actions">
              {/* <Image
                className="arrow-right"
                src={require("@/assets/images/common/arrow_right.png")}
              /> */}
              <IconRight
                className="arrow-right"
              />
            </View>
          </View>
        </View>

        {/* 收件人信息 */}
        <View className="contact-info-row">
          <View className="contact-icon-wrapper">
            <Image
            bottomOverlap={null}
              className="contact-icon"
              src={require("@/assets/images/common/receive_icon.png")}
            />
          </View>
          <View className="contact-info">
            <View className="contact-details">
              <View className="contact-details-receive">
                <View className="contact-name-row">
                  <Text className="contact-name">
                    {orderDetails.receiverName || ""}{" "}
                    {orderDetails.receiverMobile || ""}
                  </Text>

                  <IconCopy
                    className="copy-icon"
                    onClick={() =>
                      handleCopyReceiver(
                        `${orderDetails.receiverName || ""} ${
                          orderDetails.receiverMobile ||
                          orderDetails.userPhone ||
                          ""
                        } ${findAreaNameById(orderDetails.receiverAreaId)} ${
                          orderDetails.receiverDetailAddress || ""
                        }`
                      )
                    }
                  />
                </View>
                <Text className="contact-address">
                  {findAreaNameById(orderDetails.receiverAreaId)} {" "}
                  {orderDetails.receiverDetailAddress || ""}
                </Text>
              </View>
            </View>
            <View className="contact-actions">
              {/* <Image
                className="arrow-right"
                src={require("@/assets/images/common/arrow_right.png")}
              /> */}
              <IconRight
                className="arrow-right"
              />
            </View>
          </View>
        </View>
      </View>

      <View className="order-info-card-delivery">
        {/* 配送方式 */}
        <View className="contact-info-row">
          <Text className="contact-label">配送方式</Text>
          <View className="contact-info">
            <Text className="contact-value">
              {orderDetails.deliveryName || "--"}
            </Text>
          </View>
        </View>

        {/* 运费 */}
        <View className="contact-info-row">
          <Text className="contact-label">运费</Text>
          <View className="contact-info">
            <Text className="contact-value">
              ¥{(orderDetails.deliveryPrice/100 || 0).toFixed(2)}
            </Text>
          </View>
        </View>

        {/* 留言区域 */}
        <View className="contact-info-remark-row">
          <View className="contact-info-remark-row1">
            <Text className="contact-label">我的备注</Text>
            <View className="order-remark-header-detail">
              <View className="order-remark-content">
                {/* 显示所有商品的留言信息 */}
                <Input
                  className="order-remark-input-sell"
                  placeholder="请输入留言信息"
                  value={orderDetails.sellerRemark}
                  onInput={(e) => handleRemarkChange(e.detail.value)}
                  onBlur={() => saveSellerRemark()}
                />
              </View>
              <View
                className="order-remark-camera"
                onClick={handleChooseRemarkImage}
              >
                <Image
                  className="camera-icon"
                  src={require("@/assets/images/common/add_picture.png")}
                />
              </View>
            </View>
          </View>

          {/* 图片上传区域 */}
          {uploadedImages.length > 0 && (
            <View className="uploaded-images-detail">
              {uploadedImages.map((image, index) => (
                <View key={index} className="uploaded-image-item">
                  <Image src={image} className="uploaded-image" />
                  <View
                    className="delete-image-btn"
                    onClick={() => handleDeleteRemarkImage(index)}
                  >
                    ×
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>

      {orderDetails.pictureRemark || orderDetails.remark ? (
        <View className="order-remark">
          {/* 买家留言 */}
          <View className="contact-info-row">
            <Text className="contact-label">买家留言</Text>
            <View className="contact-info">
              <Text className="contact-value">
                {orderDetails.remark || "--"}
              </Text>
            </View>
          </View>

          {/* 买家留言图片 - 横向滚动 */}
          {orderDetails.pictureRemark && (
            <View className="remark-images-container">
              <View className="remark-images-scroll">
                {orderDetails.pictureRemark
                  .split(",")
                  .map((img: string, idx: number) => (
                    <Image
                      key={idx}
                      src={img.trim()}
                      className="remark-image"
                    />
                  ))}
              </View>
            </View>
          )}
        </View>
      ) : null}

      <View className="order-goods-card">
        {/* 商家信息 */}
        <View className="shop-header" onClick={(e) =>{
          e.stopPropagation();
          if(orderDetails.sellerUserId === Taro.getStorageSync("userInfo").userId){
            Taro.navigateTo({
              url: `/pageDynamic/album/index`,
            })
          }else{
            Taro.navigateTo({
              url: `/pageUserInfo/userDetail/index?userId=${orderDetails.sellerUserId}`,
            })
          }
        }}>
          <Avatar
            size="small"
            style={{ marginRight: 8 }}
            src={Taro.getStorageSync("userInfo").avatar || ""}
          />
          <Text className="shop-name">
            {Taro.getStorageSync("userInfo").nickname || ""}
          </Text>
          {/* <Image
            className="verified-icon"
            src={require("@/assets/images/common/wx_pay.png")}
          /> */}
          <IconPayment
            className="verified-icon"
          />
          <View className="shop-arrow">
            {/* <Image
              className="arrow-right"
              src={require("@/assets/images/common/arrow_right.png")}
            /> */}
            <IconRight
              className="arrow-right"
            />
          </View>
        </View>

        {/* 商品列表 */}
        {orderDetails.orderDynamics &&
          orderDetails.orderDynamics.length > 0 && (
            <View className="goods-list-detail">
              {orderDetails.orderDynamics.map(
                (dynamic: any, index: number) => {
                  const firstImage = dynamic.dynamicPictures
                    ? dynamic.dynamicPictures.split(",")[0]
                    : "";
                  return (
                    <View key={index} className="goods-item">
                      <Image src={firstImage} className="goods-img" />
                      <View className="goods-info-detail">
                        <View className="goods-title-row">
                          <Text className="goods-title">
                            {dynamic.dynamicName || "--"}
                          </Text>
                          <View className="goods-price-section">
                            <Text className="goods-price">
                              ￥{(dynamic.price/100).toFixed(2) || 0}
                            </Text>
                            <Text className="goods-quantity">
                              x{dynamic.count || 1}
                            </Text>
                          </View>
                        </View>
                        {/* 显示SKU信息 */}
                        {dynamic.orderDetails &&
                          dynamic.orderDetails.map(
                            (detail: any, detailIdx: number) => (
                              <View className="goods-sku" key={detailIdx}>
                                {/* <Text className="sku-text">
                                  {detail.colorName} {detail.specName}
                                </Text> */}

                                <Text className="sku-text">
                                  {(() => {
                                    try {
                                      const props = JSON.parse(detail.properties || "{}");
                                      return `${props.colors || ""} ${props.specifications || ""}`.trim();
                                    } catch (e) {
                                      return detail.properties; // 如果解析失败，就原样显示
                                    }
                                  })()}
                                </Text>

                                <View className="sku-count-wrapper">
                                  {detail.refundQuantity > 0 && (
                                    <Text className="refund-quantity">{`退款x${detail.refundQuantity}`}</Text>
                                  )}
                                <Text className="sku-count">
                                  {`x${detail.quantity} ${
                                    orderDetails.status === 3
                                      ? `(未发货${
                                          detail.quantity -
                                          detail.quantityShipped
                                        })`
                                      : ""
                                  }`}
                                </Text>
                                {orderDetails.status === 3 && (
                                  <Text className="sku-ems" onClick={() => {
                                    const skuImage = dynamic.dynamicPictures ? dynamic.dynamicPictures.split(",")[0] : "";
                                    const goodsInfo = {
                                      image: skuImage,
                                      title: dynamic.dynamicName || "商品名称",
                                      price: dynamic.price || "0",
                                      // spec: `${detail.colorName || ''} ${detail.specName || ''}`.trim(),
                                      spec: detail.properties,
                                      quantity: detail.quantity || 0,
                                      quantityShipped: detail.quantityShipped || 0,
                                      unshippedQuantity: (detail.quantity || 0) - (detail.quantityShipped || 0),
                                      receivingAddress: `${findAreaNameById(orderDetails.receiverAreaId)}${orderDetails?.receiverDetailAddress || ''}`
                                    };

                                    Taro.navigateTo({
                                      url: `/pageOrder/skuEms/index?orderId=${orderDetails.id}&id=${detail.orderDetailId}&goodsInfo=${encodeURIComponent(JSON.stringify(goodsInfo))}`,
                                    });
                                  }}>查看物流</Text>
                                )}
                                </View>
                              </View>
                            )
                          )}
                      </View>
                    </View>
                  );
                }
              )}
            </View>
          )}

        {/* 订单总计 */}
        <View className="order-summary-details">
          <View className="summary-row">
            <Text className="summary-label">商品数量</Text>
            <Text className="summary-value">
              {/* {orderDetails.userOrderDynamics?.reduce(
                (total: number, dynamic: any) => total + (dynamic.number || 0),
                0
              ) || 0} */}

              {orderDetails.orderDynamics[0].count || 0}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">商品总金额</Text>
            <Text className="summary-value">
              ¥{(orderDetails.totalPrice/100).toFixed(2) || 0}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">运费</Text>
            <Text className="summary-value">
              ¥{(orderDetails.deliveryPrice/100 || 0).toFixed(2)}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">实付金额</Text>
            <Text className="summary-value">
              ¥{(orderDetails.totalPrice/100+orderDetails.deliveryPrice/100).toFixed(2) || 0}
            </Text>
          </View>

          {orderDetails.orderRefund && orderDetails.orderRefund.refundAmount > 0 && (
          <View className="summary-row">
            <Text className="summary-label">退款金额</Text>
            <Text className="summary-total">
              ¥{(orderDetails.orderRefund?.refundAmount+orderDetails.orderRefund?.refundPostage || 0).toFixed(2) || 0}
            </Text>
          </View>
          )}
        </View>

        {/* <View className="order-goods-summary">
          <View>商品数量：{order.goods[0].count}</View>
          <View>商品总金额：¥{order.total}</View>
          <View>运费：¥{order.freight}</View>
          <View className="order-goods-actual">实付金额：<Text style={{ color: '#e35848' }}>¥{order.actual}</Text></View>
        </View> */}
      </View>

      {/* 物流模块 */}
      {getLogisticsData().map((logistics: any, index: number) => (
        <View key={index} className="logistics-card">
          <View className="logistics-card-content">
            <Text className="logistics-card-content-time">
              {logistics.updateTime}
            </Text>
            <Text className="logistics-card-content-status">已发货</Text>
          </View>
          {/* 物流信息头部 */}
          <View className="logistics-header">
            <View
              className="logistics-header-content"
              onClick={() => {
                const receivingAddress = `${findAreaNameById(orderDetails.receiverAreaId)}${orderDetails?.receiverDetailAddress || ''}`;

                Taro.navigateTo({
                  url: `/pageOrder/emsDetail/index?trackingNumber=${encodeURIComponent(logistics.trackingNumber)}&expressCompany=${encodeURIComponent(logistics.deliveryCompany)}&receivingAddress=${encodeURIComponent(receivingAddress)}`,
                });
              }}
            >
              <View className="logistics-status-row">
                <Text className="logistics-status">{logistics.status}</Text>
                <Text className="logistics-time">{logistics.updateTime}</Text>
              </View>
              {/* <Image
                className="arrow-right"
                src={require("@/assets/images/common/arrow_right.png")}
              /> */}
              <IconRight
                className="arrow-right"
              />
            </View>

            {/* 快递单号 */}
            <View className="logistics-tracking-row">
              <View className="logistics-tracking-info">
                <Text className="logistics-label">
                  {logistics.deliveryCompany}
                </Text>
                <View className="logistics-tracking-info">
                  <Text className="logistics-tracking-number">
                    {logistics.trackingNumber}
                  </Text>
                  {/* <Image
                    className="copy-icon"
                    src={require("@/assets/images/common/copy_icon.png")}
                    onClick={() =>
                      handleCopyTrackingNumber(logistics.trackingNumber)
                    }
                  /> */}
                  <IconCopy
                    className="copy-icon"
                    onClick={() =>
                      handleCopyTrackingNumber(logistics.trackingNumber)
                    }
                  />
                </View>
              </View>

              <View className="logistics-tracking-info">
                <Text
                  className="logistics-tracking-btn"
                  onClick={() => handleEditTrackingNumber(index)}
                >
                  修改快递单号
                </Text>
              </View>
            </View>
          </View>

          {/* 物流商品列表 */}
          <View className="logistics-goods-list">
            {logistics.goods.map((item: any, goodsIndex: number) => (
              <View key={goodsIndex} className="goods-item">
                <Image src={item.image} className="goods-img" />
                <View className="goods-info">
                  <View className="goods-title-row">
                    <Text className="goods-title">{item.title}</Text>
                    <View className="goods-price-section">
                      <Text className="goods-price">¥{item.price}</Text>
                      <Text className="goods-quantity">x{item.quantity}</Text>
                    </View>
                  </View>
                  <View className="goods-sku">
                    <Text className="sku-text">{item.spec}</Text>
                    <Text className="sku-count">x{item.quantityShipped}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>

          {/* 已发数量 */}
          <View className="logistics-summary">
            <Text className="logistics-summary-text">已发数量</Text>
            <Text className="logistics-summary-count">
              {logistics.totalQuantity}
            </Text>
          </View>
        </View>
      ))}

      <View className="order-info-card-sell">
        {/* 订单编号行 - 带复制和展开/收起功能 */}
        <View className="order-info-row">
          <Text className="order-info-label">订单编号</Text>
          <View className="order-info-value-row">
            <View className="order-info-value-row-left">
              <Text className="order-info-value">
                {orderDetails.combineOutTradeNo || orderDetails.id || "--"}
              </Text>
              {/* <Image
                className="copy-icon"
                src={require("@/assets/images/common/copy_icon.png")}
                onClick={() =>
                  handleCopyOrderNo(
                    orderDetails.combineOutTradeNo || orderDetails.id || ""
                  )
                }
              /> */}
              <IconCopy
                className="copy-icon"
                onClick={() =>
                  handleCopyOrderNo(
                    orderDetails.combineOutTradeNo || orderDetails.id || ""
                  )
                }
              />
            </View>
            {/* <Image
              className={`expand-icon ${isExpanded ? "expanded" : ""}`}
              src={require("@/assets/images/common/arrow_down.png")}
              onClick={toggleExpanded}
            /> */}
            <IconDown
              className={`expand-icon ${isExpanded ? "expanded" : ""}`}
              onClick={toggleExpanded}
            />
          </View>
        </View>

        {/* 可展开/收起的时间信息 */}
        {isExpanded && (
          <View className="expanded-time-info">
            <View className="order-info-row">
              <Text className="order-info-label">开单时间</Text>
              <Text className="order-info-value">
                {formatTime(orderDetails.createTime)}
              </Text>
            </View>
            {orderDetails.confirmReceiptTime && (
              <View className="order-info-row">
                <Text className="order-info-label">付款时间</Text>
                <Text className="order-info-value">
                  {formatTime(orderDetails.confirmReceiptTime) || "--"}
                </Text>
              </View>
            )}
            {orderDetails.cancelTime && orderDetails.status == 7 && (
              <View className="order-info-row">
                <Text className="order-info-label">取消时间</Text>
                <Text className="order-info-value">
                  {formatTime(orderDetails.cancelTime) || "--"}
                </Text>
              </View>
            )}
            <View className="order-info-row">
              <Text className="order-info-label">支付方式</Text>
              <Text className="order-info-value">微信</Text>
            </View>
          </View>
        )}
      </View>

      <View className="order-footer-bar-sell">
        {/* <Image
          className="order-footer-bar-icon"
          src={require("@/assets/images/common/share_icon.png")}
          // onClick={handleShare}
        /> */}
        <View className="order-footer-bar-icon"></View>

        {getOrderStatusText(orderDetails.status) === "待付款" && (
          <View className="order-info-cancel-btn" onClick={handleCancelOrder}>
            <Text>取消订单</Text>
          </View>
        )}

             {getOrderStatusText(orderDetails.status) === "已取消" && (
          <View className="order-info-btn-row">
            <View className="order-info-cancel-btn" onClick={handleRefundClick}>
              <Text>退款</Text>
            </View>
            <View
              className="order-footer-bar-btn"
              onClick={handleDeliveryClick}
            >
              <Text>发货</Text>
            </View>
          </View>
        )}

        {getOrderStatusText(orderDetails.status) === "待发货" && (
          <View className="order-info-btn-row">
            <View className="order-info-cancel-btn" onClick={handleRefundClick}>
              <Text>退款</Text>
            </View>
            <View
              className="order-footer-bar-btn"
              onClick={handleDeliveryClick}
            >
              <Text>发货</Text>
            </View>
          </View>
        )}

        {getOrderStatusText(orderDetails.status) === "部分未发货" && (
          <View className="order-info-btn-row">
            <View className="order-info-cancel-btn" onClick={handleRefundClick}>
              <Text>退款</Text>
            </View>
            <View
              className="order-footer-bar-btn"
              onClick={handleDeliveryClick}
            >
              <Text>发货</Text>
            </View>
          </View>
        )}

        {getOrderStatusText(orderDetails.status) === "全部已发货" && (
          <View className="order-info-cancel-btn" onClick={handleRefundClick}>
            <Text>退款</Text>
          </View>
        )}

        {getOrderStatusText(orderDetails.status) === "已完成" && (
          <View className="order-info-cancel-btn" onClick={handleRefundClick}>
            <Text>退款</Text>
          </View>
        )}

{getOrderStatusText(orderDetails.status) === "退款中" && (
   <View className="order-info-btn-row">
   <View className="order-info-cancel-btn" onClick={handleRefundClick}>
     <Text>退款</Text>
   </View>
   <View
            className="order-info-cancel-btn"
            onClick={handleRefundDetailClick}
          >
            <Text>退款详情</Text>
          </View>
 </View>
        )}

        {getOrderStatusText(orderDetails.status) === "已退款" && (
          <View
            className="order-info-cancel-btn"
            onClick={handleRefundDetailClick}
          >
            <Text>退款详情</Text>
          </View>
        )}
      </View>

      {/* 发货选择弹窗 */}
      {showDeliveryPopup && (
        <View
          className="delivery-popup-overlay"
          onClick={() => setShowDeliveryPopup(false)}
        >
          <View
            className="delivery-popup-container"
            onClick={(e) => e.stopPropagation()}
          >
            <View className="delivery-popup-content">
              <View
                className="delivery-option"
                onClick={() => handleDeliveryTypeSelect("partial")}
              >
                <Text className="delivery-option-text">部分发货</Text>
              </View>
              <View
                className="delivery-option"
                onClick={() => handleDeliveryTypeSelect("full")}
              >
                <Text className="delivery-option-text">全部发货</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* 修改快递单号弹框 */}
      <Popup
        visible={showTrackingModal}
        close={() => setShowTrackingModal(false)}
        className="tracking-modal"
      >
        <View className="tracking-modal-content">
          <View className="tracking-modal-header">
            {/* <Image
              className="close-icon"
              src={require("@/assets/images/common/syr_close_icon.png")}
              onClick={() => setShowTrackingModal(false)}
            /> */}
            <IconClose
              className="close-icon"
              onClick={() => setShowTrackingModal(false)}
            />
            <Text className="tracking-modal-title">修改快递单号</Text>
          </View>

          <View className="tracking-modal-body">
            {/* 快递单号输入 */}
            <View className="tracking-input-row">
              <Text className="tracking-input-label">快递单号</Text>
              <Input
                className="tracking-input"
                placeholder="请输入快递单号"
                value={trackingNumber}
                onInput={(e) => setTrackingNumber(e.detail.value)}
              />
            </View>

            {/* 快递公司选择 */}
            <View
              className="tracking-input-row"
              onClick={handleSelectDeliveryCompany}
            >
              <Text className="tracking-input-label">快递公司</Text>
              <View className="tracking-select">
                <Text
                  className={`tracking-select-text ${
                    !deliveryCompany ? "placeholder" : ""
                  }`}
                >
                  {deliveryCompany || "请选择快递公司"}
                </Text>
                {/* <Image
                  className="arrow-right"
                  src={require("@/assets/images/common/arrow_right.png")}
                /> */}
                <IconRight
                  className="arrow-right"
                />
              </View>
            </View>
          </View>

          <View className="tracking-modal-footer">
            <Button
              className="tracking-save-btn"
              type="primary"
              onClick={handleSaveTrackingNumber}
            >
              确定
            </Button>
          </View>
        </View>
      </Popup>

      {/* 底部弹出对话框 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
}
